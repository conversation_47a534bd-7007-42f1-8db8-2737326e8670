$(window).ready(function(){
	restoreState();
	initMap();
	updateTimeZones();
	setInterval(function(){updateShields()},60000);
	setInterval(function(){updateTimeZones()},30000);
	// checkresize();
	// window.onresize=checkresize;
});
function checkresize(){
	var mw=$('map-wrapper');
	var wh=$( window ).height();
	var wf=$( '#boxfoot' ).outerHeight();
	var wc=$( '#cabecalho' ).height();
	var ftp=$('#fTable').position();
	mw.css('height',(wh-ftp.top-5)+'px');
	mw.css('width',(wh-ftp.top-5)+'px');
}
var filters={'fAlli':[],'fPlay':[]};
var map;
var pls={};
var whs={};
var zAtual=null;
var llAtual=null;
var zDest=5;
var raio=8;
var stSelected={radius:(raio+6),color:'green',opacity:1,fillOpacity:0,fillColor:'transparent',weight:3,pane:'toptop'};
var stDetection={radius:(raio+6),color:'yellow',opacity:0.2,fillOpacity:0.4,fillColor:'yellow',weight:1,pane:'toptop','contextmenu':true};
var stDetectionOurs={radius:(raio+6),color:'white',opacity:0.2,fillOpacity:0.3,fillColor:'white',weight:1,pane:'toptop','contextmenu':true};
var stShield={radius:(raio+4),color:'white',opacity:0.5,fillOpacity:0,fillColor:'transparent',weight:2,dashArray:'4',pane:'toptop','contextmenu':true};
var stShieldOff={radius:(raio-4),color:'white',opacity:0.3,fillOpacity:0.4,fillColor:'white',weight:1,dashArray:'',pane:'toptop','contextmenu':true};
var stTeam={radius:raio,color:'blue',opacity:1,fillOpacity:0.3,fillColor:'blue',weight:1,pane:'toptop','contextmenu':true};
var stEnemy={radius:raio,color:'red',opacity:1,fillOpacity:0.3,fillColor:'red',weight:2,pane:'toptop','contextmenu':true};
var stIncomplete={radius:raio,color:'#808080',opacity:1,fillOpacity:0.8,fillColor:'#808080',weight:2,pane:'toptop','contextmenu':true};
var stWh={radius:raio,color:'white',opacity:0.4,fillOpacity:0,weight:2,pane:'toptop'};
var stWhOther={radius:raio+2,color:'yellow',fillColor:'yellow',opacity:1,fillOpacity:0.3,weight:3,pane:'toptoptemp'};
var stGrid={pane:'grid',color:'lightgreen',opacity:0.7,weight:1,dashArray:'2'};
var repIco1=null;
var repIco2=null;
var repIco3=null;
var gridIco=null;
var curPlayer=null;
var selected=null;
function setVerifShields(){
	alert('setVerifShields');
}
function setShieldsOn(pl){
	// debugger;
	$.ajax({
		url:'ajax_pc.php',
		type: 'GET',
		data: {m:'addShields', cod:pl.id},
		dataType:'script',
		success: function(data) {
		}, complete: function(){
		}
	});
}
function setShieldsOff(pl){
	// debugger;
	$.ajax({
		url:'ajax_pc.php',
		type: 'GET',
		data: {m:'removeShields', cod:pl.id},
		dataType:'script',
		success: function(data) {
		}, complete: function(){
		}
	});
}
function setShieldsUnknown(pl){
	// debugger;
	$.ajax({
		url:'ajax_pc.php',
		type: 'GET',
		data: {m:'unknownShields', cod:pl.id},
		dataType:'script',
		success: function(data) {
		}, complete: function(){
		}
	});
}
function initMap(){
	gridIco=L.divIcon({className: 'gridIco',html:'grid',iconSize:[35,16],iconAnchor:[-6,20]});
	repIco1=L.divIcon({className: 'repIco1',html:'R',iconSize:[15,16],iconAnchor:[-6,18]});
	repIco2=L.divIcon({className: 'repIco2',html:'R',iconSize:[15,16],iconAnchor:[-6,18]});
	repIco3=L.divIcon({className: 'repIco3',html:'R',iconSize:[15,16],iconAnchor:[-6,18]});
	var mapBounds=new L.latLngBounds([[0,0], [-900,900]]);
	var mapOptions={
		maxZoom: 20,
		minZoom: 0,
		crs: L.CRS.Simple,
		// maxBounds:mapBounds
	};
	var comContext=true;
	if (comContext){
		mapOptions.contextmenu=true;
	}
	
	map = L.map('pcmap', mapOptions);
	map.setView([-450, 450], 0);
	map.setMaxBounds(mapBounds);
	map.showingShields=true;
	map.showingDetection=false;
	toptop = map.createPane('toptop');
	toptop.style.zIndex = 600;
	toptoptemp = map.createPane('toptoptemp');
	toptoptemp.style.zIndex = 650;
	map.imgs=L.featureGroup([]);
	// map.imgs.addTo(map);
	map.team=L.featureGroup([],stTeam);
	map.team.addTo(map);
	map.enemy=L.featureGroup([],stEnemy);
	map.enemy.addTo(map);
	map.repair=L.featureGroup([]);
	map.repair.addTo(map);
	map.wh=L.featureGroup([],stWh);
	map.wh.addTo(map);
	map.whother=L.featureGroup([],stWhOther);
	map.whother.addTo(map);
	var imageUrl = 'images/map-bg.jpg';
	var imageBounds = [[0,0], [-900,900]];
	L.imageOverlay(imageUrl, imageBounds).addTo(map);
	if (refMap){
		// var imageUrl = 'images/468.png';
		var imageBounds = [[-5,0], [-910,905]];
		map.refMap=L.imageOverlay(refMap, imageBounds);
		map.refMap.addTo(map);
		map.refMap.setOpacity(0.5);
		var rmop=readCookieVal('opac-refMap');
		if (rmop && !isNaN(parseFloat(rmop))) {
			var op=parseFloat(rmop);
			map.refMap.setOpacity(op);
			$('#refMapOpacity').val(parseInt(op*100));
		}
	}
	var loca=readCookieVal('st-loca');
	if (loca && loca!=''){
		loca=decodeURI(loca);
		if (loca!='|'){
			vals=loca.split('|');
			if (vals[0]!='') $('#fX').val(vals[0]);
			if (vals[1]!='') $('#fY').val(vals[1]);
			showLocation();
		}
	}
	
	var whUrl = 'images/wormhole.png';// wh 564x272
	for (var k=0; k<arWhs.length;k++){
		var w=arWhs[k];
		addWh(w);
	}
	for (var k=0; k<planets.length; k++){
		var p=planets[k];
		addPlanet(p);
	}

	zAtual=map.getZoom();
	map.on('mousemove', onMapMouseMove);
	map.on('zoomend', onMapZoomend);
	map.on('click', onMapClick);
	$('.leaflet-container').css('cursor','crosshair');
	gridPane=map.createPane('grid');
	gridPane.style.zIndex = 1580;
	map.grid=L.featureGroup([],stGrid);
	var st=(readCookieVal('st-grid')=='true');
	console.log('stgrid: '+st);
	if (st) map.grid.addTo(map);
	var gl=[0,298,596,894];
	for (var k =0; k<gl.length;k++){
		for (var kk =0; kk<gl.length;kk++){
			var x=gl[k];
			var y=gl[kk];
			if (k>0 && kk>0){
				lls=[[0,x*esc],[-1190*esc,x*esc]];
				L.polyline(lls, stGrid).addTo(map.grid);
				lls=[[-1*y*esc,0],[-1*y*esc,1190*esc]];
				L.polyline(lls, stGrid).addTo(map.grid);
			}
			ll=[(-1*y-300)*esc,(x)*esc];
			var gridIco=L.divIcon({className: 'gridIco',html:(k+1)+'-'+(kk+1),iconSize:[35,16],iconAnchor:[-6,20]});
			leg=L.marker(ll,{icon:gridIco});
			leg.addTo(map.grid);
			if (kk==0 && k>0){
				ll=[(-1*y-30)*esc,(x)*esc];
				var gridIco=L.divIcon({className: 'gridIco',html:'X '+x,iconSize:[35,16],iconAnchor:[-6,20]});
				leg=L.marker(ll,{icon:gridIco});
				leg.addTo(map.grid);
			}
		}
		if (k==0){
			var x=gl[k];
			for (var kk =1; kk<gl.length;kk++){
				var y=gl[kk];
				ll=[(-1*y-30)*esc,(x)*esc];
				var gridIco=L.divIcon({className: 'gridIco',html:'Y '+y,iconSize:[35,16],iconAnchor:[-6,20]});
				leg=L.marker(ll,{icon:gridIco});
				leg.addTo(map.grid);
			}
		}
	}
	updateStats();
}
function onMapClick(ev){
	// debugger;
	var ll=llAtual;
	var y=Math.round(-1*ll.lat/esc);
	var x=Math.round(ll.lng/esc);
	limpaEditPl();
	$('#infoEditPl').show();
	$('#titEditPl').html($('#txt-addbyclick').val());
	$('#butUpdatePl').html('Insert');
	$('#hidEditAction').val('newByClick');
	$('#iep-map').val(mapN);
	$('#iep-x').val(x);
	$('#iep-y').val(y);
	limpaEditWh();
	$('#infoEditWh').show();
	$('#titEditWh').html($('#txt-addwhbyclick').val());
	$('#butUpdateWh').html('Insert');
	$('#hidEditActionWh').val('newWhByClick');
	$('#iew-map').val(mapN);
	$('#iew-x').val(x);
	$('#iew-y').val(y);
}
function limpaEditPl(){
	$('#iep-user').val('');
	$('#iep-team').val('');
	$('#iep-map').val('');
	$('#iep-x').val('');
	$('#iep-y').val('');
	$('#iep-guns').val('');
	$('#iep-level').val('');
	$('#iep-sensor').prop('checked',false);
	$('#iep-jammer').prop('checked',false);
	$('#iep-detection').prop('checked',false);
	$('#iep-repair1').prop('checked',false);
	$('#iep-repair2').prop('checked',false);
	$('#iep-repair3').prop('checked',false);
}
function limpaEditWh(){
	$('#iew-map').val('');
	$('#iew-x').val('');
	$('#iew-y').val('');
	$('#iew-maps').val('');
}
function onMapZoomend(ev){
	var z=map.getZoom();
	if (z!=zAtual) destacaPlanets();
	zAtual=z;
	var ll=llAtual;
	var y=Math.round(-1*ll.lat/esc);
	var x=Math.round(ll.lng/esc);
	var xg=Math.floor(x/298)+1;
	var yg=Math.floor(y/298)+1;
	$('#coords').html(' (zoom <b>' + z + '</b> grid <b style="color:lightgreen;">'+xg+'-'+yg+'</b>) <b>' + x + ' : ' + y + '</b>');
}
function onMapMouseMove(ev) {
	var z=map.getZoom();
	if (z!=zAtual) destacaPlanets();
	zAtual=z;
	var ll = ev.latlng;
	llAtual=ll;
	// console.log(ll.toString());
	var y=Math.round(-1*ll.lat/esc);
	var x=Math.round(ll.lng/esc);
	var xg=Math.floor(x/298)+1;
	var yg=Math.floor(y/298)+1;
	$('#coords').html(' (zoom <b>' + z + '</b> grid <b style="color:lightgreen;">'+xg+'-'+yg+'</b>) <b>' + x + ' : ' + y + '</b>');
}
function addWh(wh){
	var w=564*fator*3;
	var h=272*fator*3;
	var x=wh.pos[0];
	var y=wh.pos[1];
	var maps=wh.maps;
	var oId=wh.id;
	var whBounds = [[-1*y*esc+h/2, x*esc-w/2], [-1*y*esc-h/2, x*esc+w/2]];
	// var wh=L.imageOverlay(whUrl, whBounds,{interactive:true});
	// wh.bindTooltip('<div style="text-align:center;">wormhole</b></div>',{sticky:true});
	var d=L.circleMarker([-1*y*esc,x*esc],stWh);
	d.bindTooltip((maps && maps!=''?'Good for:<br/>'+maps:'wormhole'),{sticky:true});
	d.on('click',function(ev){infoEditWh(ev,oId)});
	whs[oId]=d;
	d.addTo(map.wh);
	// wh.addTo(map);
}
function radiusFromGuns(g){
	var r=4;
	g=parseInt(g);
	if (g>7) r=6;
	if (g>15) r=7;
	if (g>25) r=10;
	return r;
	var r=(raio-1);
	if (g>0) r+=Math.round(g/12);
	return r;
}
function addPlanet(p){
	if (!players[p.uid]){
		players[p.uid]={};
		players[p.uid].name=p.user;
		players[p.uid].alli=p.team;
	}
	var z=map.getZoom();
	var oId=p.id;
	var pl;
	var ll=[-1*p.pos[1]*esc,p.pos[0]*esc];
	var te=(p.t.toUpperCase()==myAlli);
	if (false && p.img){
		var w=p.size[0]*fator;
		var h=p.size[1]*fator;
		var planetBounds = [[-1*p.pos[1]*esc-h/2, p.pos[0]*esc-w/2], [-1*p.pos[1]*esc+h/2, p.pos[0]*esc+w/2]];
		pl=L.imageOverlay(p.img, planetBounds,{interactive:true});
	}
	// Check if planet is incomplete (plan1_lvl = -1)
	var isIncomplete = (p.gl === -1);
	var ops = isIncomplete ? stIncomplete : (te ? stTeam : stEnemy);
	pl=L.circleMarker(ll,ops);
	// debugger;
	var contextmenuItems=[
		// { text: 'Shields verified', callback: setVerifShields },
		{ text: langTxt['addshields'], callback: function(){setShieldsOn(pl)} },
		{ text: langTxt['removeshields'], callback: function(){setShieldsOff(pl)} },
		{ text: langTxt['unknownshields'], callback: function(){setShieldsUnknown(pl)} }
	];
	L.Util.setOptions(pl, {contextmenuItems: contextmenuItems});
	pl.id=oId;
	// console.log(planetBounds.toString());
	var mine=(p?.u && p.uid==uc);
	var tt='<div style="text-align:center;"><b>'+p.u+(p?.p?' ('+p.p+')':'')+'</b><br/>'+p.t;
	if (p?.g) tt+='<br>'+p.g+(p?.gl?' ('+p.gl+')':'');
	if (p.mod) tt+='<br><span style="color:gray;">'+p.mod+'</span>';
	tt+='</div>';
	pl.bindTooltip(tt,{sticky:true});
	var d=L.circleMarker(ll,ops);
	L.Util.setOptions(d, {contextmenuItems: contextmenuItems});
	d.id=oId;
	d.bindTooltip(tt,{sticky:true});
	d.g=0;
	d.r=radiusFromGuns(p.g);
	d.setRadius((z+1)*d.r);
	pl.gl=(p?.gl?parseInt(p.gl):0);
	pl.g=(p?.g?parseInt(p.g):30);
	d.setStyle({weight:(pl.gl>1?pl.gl*1.1:1),dashArray:(pl.gl==0?'3':'')});
	if (mine) d.setStyle({color:'lightgreen'});
	if (isIncomplete) d.setStyle({color:'#808080', fillColor:'#808080'});
	pl.sh=null;
	pl.shm=null;
	if (p.sh){
		pl.sh=new Date(p.sh);
	}
	d.gl=pl.gl;
	d.g=pl.g;
	pl.ring=d;
	pl.team=te;
	pl.alli=p.t;
	pl.tid=p.tid;
	pl.uid=p.uid;
	pl.shl=p.shl;
	if (pl.tid==null) pl.tid='0';
	pl.u=p.u;
	pl.filtered=false;
	if (p?.rep){
		var ico=repIco1;
		if (p.rep==2) ico=repIco2;
		if (p.rep==3) ico=repIco3;
		pl.ring.rep=L.marker(ll,{icon:ico});
		pl.ring.on('add',function(ev){this.rep.addTo(map.repair)});
		pl.ring.on('remove',function(ev){this.rep.removeFrom(map.repair)});
	}
	if(p?.det){
		pl.det=true;
		pl.detm=L.circleMarker(ll,(pl.team?stDetectionOurs:stDetection));
		var contextmenuItems=[
			// { text: 'Shields verified', callback: setVerifShields },
			{ text: langTxt['addshields'], callback: function(){setShieldsOn(pl)} },
			{ text: langTxt['removeshields'], callback: function(){setShieldsOff(pl)} },
			{ text: langTxt['unknownshields'], callback: function(){setShieldsUnknown(pl)} }
		];
		L.Util.setOptions(pl.detm, {contextMenu:true,contextmenuItems: contextmenuItems});
		pl.detm.on('click',function(ev){planetClick(ev,oId)});
		if (map.showingDetection) pl.detm.addTo((pl.team?map.team:map.enemy));
	}
	pl.on('click',function(ev){planetClick(ev,oId)});
	// pl.on('contextmenu',function(ev){planetRightClick(ev,oId)});
	d.on('click',function(ev){planetClick(ev,oId)});
	pl.ring.addTo((pl.team?map.team:map.enemy));
	pls[p.id]=pl;
	pl.addTo(map.imgs);
	// if (pl.sh){
		checkShield(oId);
		if (pl.shm) pl.shm.bindTooltip(tt,{sticky:true});
		checkDetection(oId);
		if (pl.detm) pl.detm.bindTooltip(tt,{sticky:true});
	// }
}
function updateDetection(){
	console.log('updateDetection');
	for (var k in pls){
		checkDetection(k);
	}
}
function checkDetection(id){
	var pl=pls[id];
	var ll=pl.getLatLng();
	if (!map.showingDetection){
		if (pl.detm && (pl.team?map.team:map.enemy).hasLayer(pl.detm)){
			pl.detm.removeFrom((pl.team?map.team:map.enemy));
		}
		if (pl.det && !pl.detm) {
			pl.detm=L.circleMarker(ll,(pl.team?stDetectionOurs:stDetection));
			var contextmenuItems=[
				// { text: 'Shields verified', callback: setVerifShields },
				{ text: langTxt['addshields'], callback: function(){setShieldsOn(pl)} },
				{ text: langTxt['removeshields'], callback: function(){setShieldsOff(pl)} },
				{ text: langTxt['unknownshields'], callback: function(){setShieldsUnknown(pl)} }
			];
			L.Util.setOptions(pl.detm, {contextMenu:true,contextmenuItems: contextmenuItems});
			pl.detm.on('click',function(ev){planetClick(ev,id)});
		}
		return;
	}
	if (pl.det){
		if (!pl?.detm){
			console.log('detector sem marcador');
			pl.detm=L.circleMarker(ll,(pl.team?stDetectionOurs:stDetection));
			var contextmenuItems=[
				// { text: 'Shields verified', callback: setVerifShields },
				{ text: langTxt['addshields'], callback: function(){setShieldsOn(pl)} },
				{ text: langTxt['removeshields'], callback: function(){setShieldsOff(pl)} },
				{ text: langTxt['unknownshields'], callback: function(){setShieldsUnknown(pl)} }
			];
			L.Util.setOptions(pl.detm, {contextMenu:true,contextmenuItems: contextmenuItems});
			pl.detm.on('click',function(ev){planetClick(ev,id)});
		}
		pl.detm.addTo((pl.team?map.team:map.enemy));
	}else{
		if (pl?.detm){
			console.log('no detector');
			pl.detm.removeFrom((pl.team?map.team:map.enemy));
		}
	}
}
function updateShields(){
	console.log('updateShields');
	for (var k in pls){
		checkShield(k);
	}
}
function checkShield(id){
	// if (id=='226') debugger;
	var pl=pls[id];
	if (!map.showingShields){
		if (pl.shm && (pl.team?map.team:map.enemy).hasLayer(pl.shm)){
			pl.shm.removeFrom((pl.team?map.team:map.enemy));
		}
		return;
	}
	pl.shSt=0;
	if (pl.sh){
		var dagora=new Date();
		var agora=dagora.getTime();
		var t=Math.floor((agora-pl.sh)/1000);
		console.log(agora + ' - '+pl.sh+' = '+(t/3600));
		if (t/3600>72){
			if (pl.shm){
				pl.shm.removeFrom((pl.team?map.team:map.enemy));
				pl.shm=null;
			}
		}else{
			if (pl.shl!==null && !pl.shm){
				var oId=id;
				var st=(pl.shl==false?stShieldOff:stShield);
				var r=pl.ring.getRadius();
				st.radius=(r+(pl.shl==false?(r<5?-2:(r<7?-4:-5)):3));
				pl.shm=L.circleMarker(pl.getLatLng(),(pl.shl==false?stShieldOff:stShield));
				var contextmenuItems=[
					// { text: 'Shields verified', callback: setVerifShields },
					{ text: langTxt['addshields'], callback: function(){setShieldsOn(pl)} },
					{ text: langTxt['removeshields'], callback: function(){setShieldsOff(pl)} },
					{ text: langTxt['unknownshields'], callback: function(){setShieldsUnknown(pl)} }
				];
				L.Util.setOptions(pl.shm, {contextMenu:true,contextmenuItems: contextmenuItems});
				pl.shm.on('click',function(ev){planetClick(ev,oId)});
			}
			if (!(pl.team?map.team:map.enemy).hasLayer(pl.shm)) pl.shm.addTo((pl.team?map.team:map.enemy));
			pl.shSt=(pl.shl==false?-1:1);
		}
	}else{
		if (pl.shm){
			pl.shm.removeFrom((pl.team?map.team:map.enemy));
			pl.shm=null;
		}else if (pl.shl==false){
			var oId=id;
			// pl.shm=L.circleMarker(pl.getLatLng(),(pl.shl==false?stShieldOff:stShield));
			// pl.shm.addTo((pl.team?map.team:map.enemy));
			// pl.shm.on('click',function(ev){planetClick(ev,oId)});
		}
	}
}
function infoEditWh(ev,id){
	L.DomEvent.stopPropagation(ev);
	$('#infoEditWh').show();
	$('#titEditWh').html($('#txt-updatewh').val());
	$('#butUpdateWh').html('Update');
	$('#hidEditActionWh').val('updateWh');
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'infoEditWh',id:id},
		dataType:'json',
		success: function(o) {
			for(var k in o){
				if (['maps','id','x','y','map'].indexOf(k)>=0){
					$('#iew-'+k).val(o[k]);
				}
			}
		}, complete: function(){
		}
	});
	return false;
}
function doUpdateWh(){
	var form=document.getElementById('formUpdateWh');
	try{
		var fd=new FormData(form);
	}catch(ex){
		return false;
	}
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: fd,
		dataType:'script',
		processData: false,
		contentType: false,
		success: function(data) {
		}, complete: function(){
		}
	});
	return false;
}
function planetRightClick(ev,id){
	L.DomEvent.stopPropagation(ev);
	alert(id);
}
function planetClick(ev, id){
	L.DomEvent.stopPropagation(ev);
	infoEditPl(ev,id)
}
function infoEditPl(ev,id){
	$('#infoEditPl').show();
	$('#titEditPl').html($('#txt-updateplanet').val());
	$('#butUpdatePl').html('Update');
	$('#hidEditAction').val('updatePl');
	$('#iep-repair1').prop('checked',false);
	$('#iep-repair2').prop('checked',false);
	$('#iep-repair3').prop('checked',false);
	$('#iep-detection').prop('checked',false);
	$('#iep-jammer').prop('checked',false);
	$('#iep-sensor').prop('checked',false);
	$('#iep-shield').val('null');
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'infoEditPl',id:id},
		dataType:'json',
		success: function(o) {
			// if (o.status){
				if (!players[o.uid]){
					players[o.uid]={};
					players[o.uid].name=o.user;
					players[o.uid].alli=o.team;
				}
				for(var k in o){
					if (['jaca'].indexOf(k)>=0){
						$('#iep-'+k).html(o[k]);
					}else if (['guns','level','id','user','team','x','y','map'].indexOf(k)>=0){
						$('#iep-'+k).val(o[k]);
					}else if (['shl'].indexOf(k)>=0){
						console.log('shl: '+o.shl);
						if (o.shl==null) $('#iep-shield').val('null');
						if (o.shl==true) $('#iep-shield').val('true');
						if (o.shl==false) $('#iep-shield').val('false');
					}else if (['repair'].indexOf(k)>=0){
						if (o.repair!='') $('#iep-repair'+o.repair).prop('checked',true);
					}else if (['sensor','jammer','detection'].indexOf(k)>=0){
						$('#iep-'+k).prop('checked',o[k]);
					}
				}
				$('#iep-shieldedtime').html('');
				$('#shieldedTimeTitle').hide();
				if (o.sh){
					$('#iep-addshieldbutton').hide();
					$('#iep-removeshieldbutton').show();
					var dtsh=new Date(o[k]);
					var hs=getHours(dtsh);
					// $('#iep-shieldedtime').html(dtsh.toLocaleString());
					$('#iep-shieldedtime').html(hs);
					$('#shieldedTimeTitle').show();
				}else{
					$('#iep-addshieldbutton').show();
					$('#iep-removeshieldbutton').hide();
				}
				if (o?.uid) curPlayer=o.uid; else curPlayer=null;
				if (selected) selected.removeFrom(map);
				selected=L.circleMarker(pls[id].getLatLng(),stSelected);
				selected.addTo(map);
			// }
		}, complete: function(){
		}
	});
	return false;
}
function getHours(dt){
	var d=new Date();
	var diff=d.getTime()-dt.getTime()+difServerTime;
	var hours=diff/3600000;
	var mins=Math.floor((hours-Math.floor(hours))*60);
	hours=Math.floor(hours);
	mins=(mins<10?'0':'')+mins;
	console.log('dif.: '+diff+' hm: '+hours+':'+mins);
	return hours+':'+mins;
}
function doUpdatePl(){
	var form=document.getElementById('formUpdatePl');
	try{
		var fd=new FormData(form);
	}catch(ex){
		return false;
	}
	if (fd.get('iep-map')=='') return false;
	if (fd.get('iep-x')=='') return false;
	if (fd.get('iep-y')=='') return false;
	if (fd.get('guns')=='' && fd.get('level')=='' && fd.get('iep-user')=='') return false;
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: fd,
		dataType:'script',
		processData: false,
		contentType: false,
		success: function(data) {
		}, complete: function(){
			if (selected) {
				selected.removeFrom(map);
				selected=null;
			}
		}
	});
	return false;
}
function addShields(){
	var id=$('#iep-id').val();
	if (id=='') return false;
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'addShields',cod:id},
		dataType:'script',
		success: function(data) {
		}, complete: function(){
		}
	});
	return false;
}
function removeShields(){
	var id=$('#iep-id').val();
	if (id=='') return false;
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'removeShields',cod:id},
		dataType:'script',
		success: function(data) {
		}, complete: function(){
		}
	});
	return false;
}
function closeEditPl(){
	$('#infoEditPl').hide();
	if (selected) {
		selected.removeFrom(map);
		selected=null;
	}
	return false;
}
function excludeWh(id){
	var l=whs[id];
	if (typeof l == 'undefined') return;
	if (map.hasLayer(l)) l.removeFrom(map);
	delete(whs[id]);
}
function excludePlanet(id){
	var l=pls[id];
	if (typeof l == 'undefined') return;
	if (map.hasLayer(l)) l.removeFrom(map);
	var from=(l.team?map.team:map.enemy);
	if (from.hasLayer(l.ring)) l.ring.removeFrom(from);
	if (l.shm && from.hasLayer(l.shm)) l.shm.removeFrom(from);
	delete(pls[id]);
}
function destacaPlanets(){
	var z=map.getZoom();
	if (zAtual<zDest){
		if (z>=zDest) {
			desligaDestaque();
		}else{
			setRaios(z);
		}
	}else{
		if (z<zDest) {
			setRaios(z);
			ligaDestaque(z);
		}
	}
}
function setRaios(z){
	var nr=(z+1);
	console.log('novo raio '+nr);
	map.team.eachLayer(function(d){
		d.setRadius(nr*d.r);
	});
	map.enemy.eachLayer(function(d){d.setRadius(nr*d.r)});
	map.wh.eachLayer(function(d){d.setRadius(nr*raio)});
}
function ligaDestaque(z){
	map.team.addTo(map);
	map.enemy.addTo(map);
	map.wh.addTo(map);
	map.imgs.removeFrom(map);
}
function desligaDestaque(){
	map.team.removeFrom(map);
	map.enemy.removeFrom(map);
	map.wh.removeFrom(map);
	map.imgs.addTo(map);
}
function addFilter(which, val, txt){
	if (filters[which].indexOf(val)>-1) {
		removeFilter(which, val);
	}else{
		filters[which].push(val);
		var fb=$('<div></div');
		fb.addClass('floatfiltro');
		fb.attr('id','ff-'+which+'-'+val);
		txt.replace('"','\"');
		fb.html('<i class="fa fa-close fafiltro" onclick="removeFilter(\''+which+'\',\''+val+'\')"></i>&nbsp;<div class="txtfloatfiltro" title="'+txt+'">'+txt+'</div>');
		$('#fbox-'+which).append(fb);
		doFilters();
	}
}
function removeFilter(which, val){
	filters[which].splice(filters[which].indexOf(val), 1);
	$('#ff-'+which+'-'+val).remove();
	doFilters();
}
function doFilters(){
	var z=map.getZoom();
	var txt='';
	// var fi=$('#fUser');
	// txt=fi.val();
	// txt=txt.toUpperCase().trim();
	var guns=$('#fGuns').val();
	var gl=$('#fGunLevel').val();
	if (filters.fAlli.length==0 && filters.fPlay.length==0 && txt=='' && guns=='' && gl==''){
		for (var t in pls){
			var l=pls[t];
			var from=(l.team?map.team:map.enemy);
			l.filtered=false;
			if (!map.imgs.hasLayer(l)) l.addTo(map.imgs);
			if (!map.hasLayer(l.ring)) {
				l.ring.addTo(from);
			}
			l.ring.setStyle({fillOpacity:from.options.fillOpacity,opacity:from.options.opacity});
			if (l.shm) {
				if (l.shSt==1) {
					l.shm.setStyle({opacity:0.5,fillOpacity:0});
				}else if (l.shSt==-1){
					l.shm.setStyle({opacity:0.3,fillOpacity:0.4});
				}
			}
			if (l.detm) {
				l.detm.setStyle({opacity:0.2,fillOpacity:0.3});
			}
		}
		$('#filterSummary').html('');
		return;
	}
	var count=0;
	for (var t in pls){
		var l=pls[t];
		var vai=true;
		if (filters.fAlli.length>0){
			var dentro=false;
			if (filters.fAlli.indexOf('ours')>=0 && l.team==true){
				dentro=true;
			}else if (filters.fAlli.indexOf('theirs')>=0 && l.team==false){
				dentro=true;
			}
			if (filters.fAlli.indexOf(l.tid)<0 && !dentro){
				vai=false;
			}
		}
		if (filters.fPlay.length>0){
			if (filters.fPlay.indexOf(l.uid)<0){
				vai=false;
			}
		}
		if (vai && txt!=''){
			if (l.u.toUpperCase().indexOf(txt)<0){
				vai=false;
			}
		}
		if (vai && guns!=''){
			if (l?.g){
				if (parseInt(l.g)>=parseInt(guns)){
					vai=false;
				}
			}else{
				vai=false;
			}
		}
		if (vai && gl!=''){
			if (l?.gl){
				if (parseInt(l.gl)!=parseInt(gl)){
					vai=false;
				}
			}else{
				vai=false;
			}
		}

		var from=(l.team?map.team:map.enemy);
		if (vai){
			count++;
			l.filtered=false;
			if (!map.imgs.hasLayer(l)) l.addTo(map.imgs);
			if (!from.hasLayer(l.ring)) {
				l.ring.addTo(from);
			}
			l.ring.setStyle({fillOpacity:0.5,opacity:from.options.opacity});
			if (l.shm) {
				if (l.shSt==1) {
					l.shm.setStyle({opacity:0.5,fillOpacity:0});
				}else if (l.shSt==-1){
					l.shm.setStyle({opacity:0.3,fillOpacity:0.4});
				}
			}
			if (l.detm) {
				l.detm.setStyle({opacity:0.2,fillOpacity:0.3});
			}
		}else{
			l.filtered=true;
			if (map.imgs.hasLayer(l)) l.removeFrom(map.imgs);
			if (from.hasLayer(l.ring)) {
				// l.ring.removeFrom(from);
				l.ring.setStyle({fillOpacity:0.1,opacity:0.25});
			}
			if (l.shm) {
				if (l.shSt==1) {
					l.shm.setStyle({opacity:0.2,fillOpacity:0});
				}else if (l.shSt==-1){
					l.shm.setStyle({opacity:0.1,fillOpacity:0.2});
				}
			}
			if (l.detm) {
				l.detm.setStyle({opacity:0.1,fillOpacity:0.1});
			}
		}
	}
	if (count==0){
		$('#filterSummary').html('No planets filtered');
	}else{
		$('#filterSummary').html('Filtered: <b>'+count+(count==1?' planet':' planets')+'</b>');
	}
}
function filterUser(){
	var fi=$('#fUser');
	var txt=fi.val();
	txt=txt.toUpperCase();
	for (var t in pls){
		l=pls[t];
		if (l.u.toUpperCase().indexOf(txt)<0){
			if (map.hasLayer(l)) l.removeFrom(map);
			if (map.hasLayer(l.ring)) l.ring.removeFrom((l.te?map.team:map.enemy));
		}else{
			if (!map.hasLayer(l) && l.filtered==false) l.addTo(map);
			if (!map.hasLayer(l.ring)) l.ring.addTo((l.te?map.team:map.enemy));
		}
	}
}
function newAlliance(){
	var h='<div id="formNewAlliance" class="content" style="min-height:50px;><div class="row"><div class="col-sm-12">';
	h+='<div class="form-group"><label>Alliance</label><input class="form-control" type="text" name="alli1_nam" id="na-alli1_nam" value="" placeholder="Alliance name"/>';
	h+='</div></div></div></div>';
	var d=$(h);
	d.dialog({
		modal:true,
		width:400,
		height:'auto',
		closeText:'Close',
		title: 'Edit player',
		dialogClass: 'default',
		buttons: [
			{
			text: "Close",
			click: function() {
				$( this ).dialog( "close" );
			}
			,showText: false
			},
			{
				text: "Create",
				click: function() {
					doNewAlliance();
					$( this ).dialog( "close" );
				}
				,showText: false
				}
		]
	});
}
function doNewAlliance(){
	var name=$('#na-alli1_nam').val();
	if (name.trim()=='') return alert('Alliance name is required');
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'doNewAlliance',name:name},
		dataType:'script',
		success: function(data) {
		}, complete: function(){
		}
	});
}
function editPlay(){
	if (!curPlayer) return alert('Player not defined');
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'formEditPlay'},
		dataType:'html',
		success: function(h) {
			$('#portaFormEditPlayer').html(h);
			$('#ep-alli1_cod').val(players[curPlayer].alli);
			$('#ep-play1_nam').val(players[curPlayer].name);
			$('#portaFormEditPlayer').dialog({
				modal:true,
				width:400,
				height:'auto',
				closeText:'Close',
				title: 'Edit player',
				dialogClass: 'default',
				buttons: [
					{
					text: "Close",
					click: function() {
						$( this ).dialog( "close" );
					}
					,showText: false
					},
					{
						text: "Update",
						click: function() {
							doEditPlay();
							$( this ).dialog( "close" );
						}
						,showText: false
						}
				]
			});
				}, complete: function(){
		}
	});
	return;
}
function doEditPlay(){
	var name=$('#ep-play1_nam').val();
	var alli=$('#ep-alli1_cod').val();
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'doEditPlay',play:curPlayer,name:name,alli:alli},
		dataType:'script',
		success: function(data) {
		}, complete: function(){
		}
	});
}
function cleanFilterUser(){

}
function parseFname(){
	var user=null;
	var team=null;
	var x=null;
	var y=null;
	var finp = document.getElementById('plfile');
	if (finp.files.length==0) return;
	var fname = finp.files[0].name;

	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'parseFname',f:fname},
		dataType:'script',
		success: function(data) {
		}, complete: function(){
		}
	});
}
function preUpload(){
	var form=document.getElementById('formUpload');
	try{
		var fd=new FormData(form);
	}catch(ex){
		return false;
	}
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: fd,
		dataType:'script',
		processData: false,
		contentType: false,
		success: function(data) {
		}, complete: function(){
		}
	});
	return false;
}
function doUpload(){
	if ($('#hidUploadAction').val()=='upload') return false;
	var form=document.getElementById('formUpload');
	try{
		var fd=new FormData(form);
	}catch(ex){
		return false;
	}
	$('#butSubmitUpload').attr('disabled',true);
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: fd,
		dataType:'script',
		processData: false,
		contentType: false,
		success: function(data) {
			limpaForm();
		}, complete: function(){
			$('#butSubmitUpload').attr('disabled',false);
			$('#hidUploadAction').val('upload');
		}
	});
	return false;
}
function limpaForm(){
	$('#formUpload :input').each(function(k, el){
		if (el.tagName!='INPUT') return;
		if (el.name=='map' || el.name=='m') return;
		$(el).val('');
	});
	$('#obsUser').html('');
	$('#obsTeam').html('');
}
function preUploadMap(){
	var form=document.getElementById('formUploadMap');
	try{
		var fd=new FormData(form);
		// Add current map number as fallback
		fd.append('currentMap', mapN);
	}catch(ex){
		return false;
	}
	$('#butSubmitUploadMap').attr('disabled',true).text('Processing...');
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: fd,
		dataType:'script',
		processData: false,
		contentType: false,
		success: function(data) {
			// Success handled by server response
		}, complete: function(){
			$('#butSubmitUploadMap').attr('disabled',false).text('Upload & Process Map');
		}
	});
	return false;
}
function mapRefOpacity(slider){
	var v=$(slider).val();
	v/=100;
	map.refMap.setOpacity(v);
	jsSetCookie('opac-refMap',v,null,'/');
}
var lineX=null;
var lineY=null;
function showLocation(lock){
	if (typeof lock == 'undefined') lock=false;
	if (lineX && map.hasLayer(lineX)) lineX.removeFrom(map);
	if (lineY && map.hasLayer(lineY)) lineY.removeFrom(map);
	var x=$('#fX').val();
	x=parseInt(x);
	if (x<0 || x>1190) x=null;
	var y=$('#fY').val();
	y=parseInt(y);
	if (y<0 || y>1190) y=null;
	if (x){
		lls=[[0,x*esc],[-1190*esc,x*esc]];
		lineX=L.polyline(lls, {color: 'white',weight:1,opacity:1,dashArray:'6'}).addTo(map);
		if (lock) setTimeout(function(){if (map.hasLayer(lineX)) lineX.removeFrom(map)},3000);
	}
	if (y){
		lls=[[-1*y*esc,0],[-1*y*esc,1190*esc]];
		lineY=L.polyline(lls, {color: 'white',weight:1,opacity:1,dashArray:'6'}).addTo(map);
		if (lock) setTimeout(function(){if (map.hasLayer(lineY)) lineY.removeFrom(map)},3000);
	}
	jsSetCookie('st-loca',(x?x:'')+'|'+(y?y:''),null,'/');
}
function clearLocation(){
	$('#fX').val('');
	$('#fY').val('');
	if (map.hasLayer(lineX)) lineX.removeFrom(map);
	if (map.hasLayer(lineY)) lineY.removeFrom(map);
	jsSetCookie('st-loca','|',null,'/');
}
function toggleGrid(){
	var st=true;
	if (map.hasLayer(map.grid)){
		map.grid.removeFrom(map);
		st=false;
	}else{
		map.grid.addTo(map);
	}
	jsSetCookie('st-grid',st,null,'/');
}
function updateStats(){
	var total=379
	var allies=0;
	var enemies=0;
	var alliCods={'No alliance':0};
	var alliCounts={'No alliance':0};
	for (var t in pls){
		if (pls[t]?.tid){
			if (!alliCods[pls[t].alli]){
				alliCods[pls[t].alli]=pls[t].tid;
			}
		}
		if (pls[t].team) {
			allies++;
			if (!alliCounts[pls[t].alli]) alliCounts[pls[t].alli]=0;
			alliCounts[pls[t].alli]++;
		} else {
			enemies++;
			if (pls[t]?.alli && pls[t].alli!='---'){
				if (!alliCounts[pls[t].alli]) alliCounts[pls[t].alli]=0;
				alliCounts[pls[t].alli]++;
			}else{
				alliCounts['No alliance']++;
			}
		}
	}
	var max=0;
	var alliNames=[];
	for (var k in alliCounts){
		if (alliCounts[k]>max) max=alliCounts[k];
		alliNames.push(k);
	}
	alliNames.sort(function(a,b){return alliCounts[b]-alliCounts[a]});
	max=total;
	var unmappedEnemies=total-allies-enemies;
	var h='<table id="totalsTable"><tr><td><b>'+langTxt['planetsonmap']+'</b></td><td align="right">~'+total+'</td><td></td></tr>';
	h+='<tr><td><b style="color:blue;">'+langTxt['allies']+'</b></td><td align="right">'+allies+'</td><td align="right">('+(Math.round(allies/total*10000)/100)+'%)</td></tr>';
	h+='<tr><td><b style="color:red;">'+langTxt['mappedenemies']+'</b></td><td align="right">'+enemies+'</td><td align="right">('+(Math.round(enemies/total*10000)/100)+'%)</td></tr>';
	h+='<tr><td><b style="color:gray;">'+langTxt['unmappedenemies']+'</b></td><td align="right">~'+unmappedEnemies+'</td><td align="right">('+(Math.round(unmappedEnemies/total*10000)/100)+'%)</td></tr>';
	h+='</table>';
	h+='<table id="tabGraf"><tr>';
	h+='<td style="width:'+Math.round(allies/total*100)+'%;background-color:blue;"></td>';
	h+='<td style="width:'+Math.round(enemies/total*100)+'%;background-color:red;"></td>';
	h+='<td style="width:'+Math.round(unmappedEnemies/total*100)+'%;background-color:lightgray;"></td>';
	h+='</tr></table>';
	h+='<b>'+langTxt['distribution']+'</b> ('+langTxt['mapped']+')<br/>';
	h+='<table width="100%" id="distTable">';
	for (var n=0; n<alliNames.length; n++){
		k=alliNames[n];
		var color='red';
		if (k==myAlli) color='blue';
		if (k=='No alliance') color='lightgray';
		var tn=k;
		if (k in alliCods) tn='<span class="link" onclick="addFilter(\'fAlli\',\''+alliCods[k]+'\',\''+k+'\')">'+k+'</span>';
		h+='<tr><td style="width:150px;">'+tn+'</td><td style="width:50px;text-align:center;">'+alliCounts[k]+'</td><td style="min-width:100px;"><div style="background-color:'+color+';height:16px;display:inline-block;width:'+Math.round(alliCounts[k]/max*100)+'%"></div><div class="distPercItem">'+(Math.round(alliCounts[k]/total*10000)/100)+'%</div></td></tr>';
	}
	h+='</table>';
	$('#divStats').html(h);
}
function toggle(id){
	var icoId=id.replace('div','ico');
	var vis=false;
	if ($('#'+id).is(':visible')){
		$('#'+id).hide();
		if (icoId) {
			$('#'+icoId).removeClass('fa-caret-up');
			$('#'+icoId).addClass('fa-caret-down');
		}
	}else{
		vis=true;
		$('#'+id).show();
		if (icoId) {
			$('#'+icoId).addClass('fa-caret-up');
			$('#'+icoId).removeClass('fa-caret-down');
		}
	}
	jsSetCookie('st-'+id, vis, null, '/');
}
function restoreState(){
	var panels=['divFilters','divStats','divHelp','divLista'];
	for (var k=0; k<panels.length;k++){
		var st=(readCookieVal('st-'+panels[k])=='true');
		if (st!=$('#'+panels[k]).is(':visible')){
			toggle(panels[k]);
		}
	}
}
function goToMap(mapN){
	if(mapN!='' && !isNaN(parseInt(mapN))) {
		var url='index.php?map='+mapN;
		if (mapN) url+='&map='+mapN;
		document.location=url;
	}
}
function enterGoToMap(ev){
	if (ev.which==13) {
		goToMap($('#fmap').val());
	}
}
function showWhFromMap(mapN){
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'showWhFromMap',map:mapN},
		dataType:'json',
		success: function(data) {
			showWhOtherMap(data);
		}
	});
}
function showWhOtherMap(whs){
	for(var k=0; k<whs.length; k++){
		var wh=whs[k];
		var w=564*fator*3;
		var h=272*fator*3;
		var x=wh.pos[0];
		var y=wh.pos[1];
		var maps=wh.maps;
		var oId=wh.id;
		var whBounds = [[-1*y*esc+h/2, x*esc-w/2], [-1*y*esc-h/2, x*esc+w/2]];
		// var wh=L.imageOverlay(whUrl, whBounds,{interactive:true});
		// wh.bindTooltip('<div style="text-align:center;">wormhole</b></div>',{sticky:true});
		var d=L.circleMarker([-1*y*esc,x*esc],stWhOther);
		d.bindTooltip((maps && maps!=''?wh.map+' good for:<br/>'+maps:'wormhole '+wh.map),{sticky:true});
		d.addTo(map.whother);
	}
	var jaca=setTimeout(function(){map.whother.eachLayer(function(d){d.removeFrom(map.whother)})},30000);
}
function updateTimeZones(){
	var dt=new Date();
	var tz=dt.getTimezoneOffset();
	// console.log('tz='+tz);
	var tsUtc=dt.getTime();
	// console.log(dt.toISOString());
	// console.log(gameOffset);
	var gameTime=new Date(dt.getTime()+gameOffset*1000);
	var brTime=new Date(dt.getTime()+brOffset*1000);
	var mxTime=new Date(dt.getTime()+mxOffset*1000);
	var caTime=new Date(dt.getTime()+caOffset*1000);
	var ilTime=new Date(dt.getTime()+ilOffset*1000);
	// console.log(gameTime.toISOString());
	$('#gametime').text(showHora(gameTime));
	$('#brtime').text(showHora(brTime));
	$('#mxtime').text(showHora(mxTime));
	$('#catime').text(showHora(caTime));
	$('#iltime').text(showHora(ilTime));

}
function showHora(dt, secs=false){
	var h=dt.getUTCHours();
	var m=dt.getUTCMinutes();
	var s=dt.getUTCSeconds();
	var hs=(h<10?'0':'')+h+':'+(m<10?'0':'')+m;
	if (secs) hs+=':'+(s<10?'0':'')+s;
	return hs;
}
function toggleNavShield(){
	if (map.showingShields){
		map.showingShields=false;
		map.showingDetection=true;
	}else{
		map.showingShields=true;
		map.showingDetection=false;
	}
	updateShields();
	updateDetection();
}
function toggleImpUser(){
	var actualPlayer=uc;
	$.ajax({
		url:'ajax_pc.php',
		type: 'POST',
		data: {m:'impUser',ruc:ruc,user:actualPlayer,lang:lang},
		dataType:'html',
		success: function(h) {
			var d=$('<div id="impUserDialog" title="View as user"></div>');
			d.html(h);
			d.dialog({
				modal:true,
				width:400,
				height:'auto',
				closeText:'Close',
				title: 'Edit player',
				dialogClass: 'default',
				buttons: []
			});
			}, complete: function(){
		}
	});
	return;
}