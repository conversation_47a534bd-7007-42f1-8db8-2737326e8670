<?php

/**
 * Esta classe extende a <code>PLANET_REL</code> adicionando <b>toda a implementação de lógica de negócio relativa a PLANET</b>.<br>
 * Por favor <b>modifique esta classe</b>. Não faça implementações nas classes <code>PLANET_BASE</code> e <code>PLANET_REL</code>.<br>
 * Não instancie diretamente esta classe; ao invés disso utilize a classe <code>FACTORY</code>. Ex: <code>$obj = &FACTORY_lab::PLANET()</code>.
 * <AUTHOR> (<EMAIL>)
 */
class PLANET extends PLANET_REL { 
	function toMapJson($imgPath=null){
		global $acao;
		if (!$imgPath) $imgPath=getImgPath();
		$arAlli=getArrayAlli();
		$playName='UNKNOWN';
		$playCod=null;
		$play=$this->getPLAYER();
		if (!$play) {
			if (!empty($this->plan1_own)){
				if ($this->plan1_own=='UNKNOWN'){
					$playName='UNKNOWN';
				}else{
					$play=&FACTORY_lab::PLAYER();
					$play->play1_nam=$this->plan1_own;
					$play->alli1_cod=$this->alli1_cod;
					if (!$play->insert()){
						die('Não inseriu player '.$this->plan1_own);
					}else{
						$this->play1_cod=$play->play1_cod;
						$this->update();
					}
					$playName=$play->play1_nam;
					$playCod=$play->play1_cod;
				}
			}
		}else{
			$playName=$play->play1_nam;
			$playCod=$play->play1_cod;
			if (empty($play->alli1_cod) && !empty($this->alli1_cod)){
				$play->alli1_cod=$this->alli1_cod;
				$play->update();
			}
		}
		if ($play && !empty($play->alli1_cod)){
			$alliCod=$play->alli1_cod;
			$alliNam=$arAlli[$alliCod]->alli1_nam;
		}else{
			$alliCod=null;
		}

		if (empty($this->plan1_img) || !file_exists($imgPath.$this->plan1_img)) {
			$s=[0,0];
			$img=null;
		}else{
			// echo $imgPath.$this->plan1_img."<br/>";
			$s=getimagesize($imgPath.$this->plan1_img);
			$img='images/planets/'.$this->plan1_img;
		}
		if (empty($this->plan1_dat)){
			$txtMod=null;
		}else{
			$dtime=time()-$this->plan1_dat;
			if ($dtime<120){
				$txtMod=$dtime.'s';
			}elseif($dtime<60*60){
				$txtMod=round($dtime/60).'m';
			}elseif($dtime<24*60*60){
				$h=floor($dtime/3600);
				$m=round(($dtime % 3600)/60);
				$txtMod=$h.'h '.$m.'m';
			}elseif($dtime<3*24*60*60){
				$d=floor($dtime/86400);
				$m=$dtime%86400;
				$h=floor($m/3600);
				$m=round(($m % 3600)/60);
				$txtMod=$d.'d '.$h.'h '.$m.'m';
			}else{
				$d=floor($dtime/86400);
				$m=$dtime%86400;
				$h=floor($m/3600);
				$m=round(($m % 3600)/60);
				$txtMod=$d.'d '.$h.'h';
			}
		}
		$tssh=null;
		if (!empty($this->plan1_ts_shield)){
			if($this->plan1_ts_shield>time()-72*60*60){
				$tssh=$this->plan1_ts_shield*1000;
			}
		}
		$o=[
			'img'=>$img,
			'pos'=>[$this->plan1_x,$this->plan1_y],
			'size'=>[$s[0],$s[1]],
			'u'=>$playName,
			'uid'=>$playCod,
			't'=>(empty($alliCod)?'---':$alliNam),
			'dt'=>$this->plan1_dat*1000,
			'mod'=>$txtMod,
			'tid'=>(empty($alliCod)?null:$alliCod),
			'id'=>$this->plan1_cod,
			'sh'=>$tssh,
			'shl'=>$this->plan1_shl,
			'det'=>($this->plan1_det===true),
		];
		if (!empty($this->plan1_repair)) $o['rep']=$this->plan1_repair;
		if (!empty($this->plan1_guns)) $o['g']=$this->plan1_guns;
		if (!empty($this->plan1_lvl) || $this->plan1_lvl == -1) $o['gl']=$this->plan1_lvl;
		if (@$acao->pessoa->isRoleAdm) $o['p']=$this->plan1_cod;
		return $o;
	}
}
?>