<?php
global $db_lab, $sqlCompleto;
include_once('codebase_lab/function/func_lab.inc');
if (empty($mapN)) $mapN=468;

$method=$_REQUEST['m'];
if (function_exists($method)){
	call_user_func($method);
}
exit;
function upload(){ // pre-upload
	global $db_lab, $sqlCompleto;
	$db_lab->BeginTrans();
	$teamFrom='undefined';
	$userFrom='undefined';
	$locaFrom='undefined';
	$teamFound=false;
	$userFound=false;

	$esc=900/1190;
	$fator=20/1100;
	$imgPath=getImgPath();
	$arPlay=getArrayPlay();
	ob_start();
	// echo nl2br(print_r($_REQUEST, true))."<br/>";
	// echo nl2br(print_r($_FILES, true))."<br/>";
	$fname=$_FILES['plfile']['name'];
	$base=pathinfo($fname,PATHINFO_BASENAME );
	$base=strtoupper(substr($base, 0, strrpos($base, '.')));
	// die('aqui '.$base);
	$nacos=explode('-',$base);
	if (count($nacos)==3){
		$user=strtoupper(trim($nacos[0]));
		$team=strtoupper(trim($nacos[1]));
		if (substr($team, 0, 4)=='INSO') $team='INSOMNIACS';
		$pos=$nacos[2];
		$pnacos=explode('X',$pos);
		if (count($pnacos)==2){
			$x=$pnacos[0];
			$y=$pnacos[1];
		}
		$teamFrom='filename';
		$userFrom='filename';
		$locaFrom='filename';
	}elseif(count($nacos)==4){
		$user=strtoupper(trim($nacos[0]));
		$team=strtoupper(trim($nacos[1]));
		if (substr($team, 0, 4)=='INSO') $team='INSOMNIACS';
		$x=$nacos[2];
		$y=$nacos[3];
		$teamFrom='filename';
		$userFrom='filename';
		$locaFrom='filename';
	}
	$ext=pathinfo($fname,PATHINFO_EXTENSION );

	if (!empty($_REQUEST['team'])) {
		$team=trim($_REQUEST['team']);
		$teamFrom='input';
	}
	$map=$_REQUEST['map'];
	if (!empty($_REQUEST['x']) && !empty($_REQUEST['y'])){
		$x=$_REQUEST['x'];
		$y=$_REQUEST['y'];
		$locaFrom='input';
	} 
	if (!empty($_REQUEST['user'])) {
		$user=trim($_REQUEST['user']);
		$userFrom='input';
	}
	$alli=null;
	if (!empty($team)){
		$alli=getAlliByName($team);
	}
	$play=&FACTORY_lab::PLAYER();
	$sql="select ".$play->getAster()." 
	from tb1_play where upper(trim(play1_nam))=upper(trim(?)) 
	or (
		length(?)>3 
		and upper(trim(substring(play1_nam from 1 for length(?))))=upper(trim(?))
	)";
	$params=[['varchar',$user],['varchar',$user],['varchar',$user],['varchar',$user]];
	if (!$play->load($sql, $params)) {
		$play=null;
	}else{
		$userFound=true;
	}
	// echo nl2br(print_r($play, true))."<br/>";exit;
	$s=getimagesize($_FILES['plfile']['tmp_name']);
	echo nl2br(print_r($s, true))."<br/>";

	// verificação de sobreposição
	echo $fname." fname<br/>";
	$jaExiste=false;
	$planCod=null;
	$w=$s[0]*$fator;
	$h=$s[1]*$fator;
	$xmin=$x-$w/2/$esc;
	$xmax=$x+$w/2/$esc;
	$ymin=$y-$h/2/$esc;
	$ymax=$y+$h/2/$esc;
	$plan=&FACTORY_lab::PLANET();
	$sql="select ".$plan->getAster()." 
	from tb1_plan 
	where plan1_x between ? and ?
	and plan1_y between ? and ?";
	$params=[['integer',$xmin],['integer',$xmax],['integer',$ymin],['integer',$ymax]];
	if (!$plan->load($sql, $params)){
		$plan=null;
	}
	$allowed=true;
	$msg=[];
	$sep='';
	if ($plan){
		$msg[]='UPDATE OF EXISTING PLANET!';
		$pPlay=$plan->getPLAYER();
		if ($play){
			if ($pPlay->play1_cod!=$play->play1_cod){
				$msg[]='New player: '.$play->play1_nam.' | Original player: '.$pPlay->play1_nam;
			}else{
				$msg[]='Same player: '.$play->play1_nam;
			}
		}else{
			$msg[]='New player not found (will be created): '.$user.' | Original player: '.$pPlay->play1_nam;
		}
		$pAlli=$plan->getALLIANCE();
		if ($alli){
			if ($pAlli){
				if ($pAlli->alli1_cod!=$alli->alli1_cod){
					$msg[]='New alliance: '.$alli->alli1_nam.' | Original alliance: '.$pAlli->alli1_nam;
				}else{
					$msg[]='Same alliance: '.$alli->alli1_nam;
				}
			}else{
				if ($alli){
					$msg[]='New alliance: '.$alli->alli1_nam.' | Original alliance: NONE';
				}else{
					$msg[]='No alliance supplied | Original alliance: NONE';
				}
			}
		}
		$output=ob_get_clean();
		$msg[]='>> Confirm update on planet info?';
		$txt=implode("\\n",$msg);
		die("if (confirm('".$txt."')) {\$('#hidUploadAction').val('upload_go');doUpload();}else{limpaForm();}");
	}else{
		$output=ob_get_clean();
		if ($play){
			$msg[]='Player: '.$play->play1_nam;
		}else{
			$msg[]='!!! New player: '.$user;
		}
		if ($alli){
			$msg[]='Alliance: '.$alli->alli1_nam;
		}else{
			$msg[]='!!! New alliance: '.$team;
		}
		$txt=implode("\\n",$msg);
		die("\$('#upUser').val('".($play?$play->play1_nam:$user)."');\$('#upTeam').val('".($alli?$alli->alli1_nam:$team)."');\$('#upX').val('".$x."');\$('#upY').val('".$y."');\$('#hidUploadAction').val('upload_go');doUpload();");
	}
	die("alert('ERROR');");
}
function parseFname(){
	global $db_lab, $sqlCompleto;
	$fname=$_REQUEST['f'];
	$base=pathinfo($fname,PATHINFO_BASENAME );
	$base=strtoupper(substr($base, 0, strrpos($base, '.')));
	// die('aqui '.$base);
	$nacos=explode('-',$base);
	$parseOk=false;
	if (count($nacos)==3){
		$user=strtoupper(trim($nacos[0]));
		$team=strtoupper(trim($nacos[1]));
		if (substr($team, 0, 4)=='INSO') $team='INSOMNIACS';
		$pos=$nacos[2];
		$pnacos=explode('X',$pos);
		if (count($pnacos)==2){
			$x=$pnacos[0];
			$y=$pnacos[1];
			if (intval($x)>=0 && intval($x)<=1190 && intval($y)>=0 && intval($y)<=1190) $parseOk=true;
		}
	}elseif(count($nacos)==4){
		$user=strtoupper(trim($nacos[0]));
		$team=strtoupper(trim($nacos[1]));
		if (substr($team, 0, 4)=='INSO') $team='INSOMNIACS';
		$x=$nacos[2];
		$y=$nacos[3];
		if (intval($x)>=0 && intval($x)<=1190 && intval($y)>=0 && intval($y)<=1190) $parseOk=true;
	}
	if (!$parseOk) die("alert('Error parsing file name')");
	$txtTeam='ND';
	$alliCod=null;
	if (!empty($team)){
		$alli=getAlliByName($team);
		if (!$alli){
			$txtTeam='new';
		}else{
			$alliCod=$alli->alli1_cod;
			$team=$alli->alli1_nam;
			$txtTeam='existing';
		}
	}
	$txtUser='ND';
	if (!empty($user)){
		$play=getPlayByName($user,$alliCod);
		if (!$play){
			$txtUser='new';
		}else{
			$txtUser='existing';
			$user=$play->play1_nam;
		}
	}
	?>$('#upUser').val('<?=$user?>');$('#upTeam').val('<?=$team?>');$('#obsUser').html('<?=$txtUser?>');$('#obsTeam').html('<?=$txtTeam?>');$('#upX').val('<?=$x?>');$('#upY').val('<?=$y?>');<?
}
function upload_go(){
	global $db_lab, $sqlCompleto;
	$db_lab->BeginTrans();
	$esc=900/1190;
	$fator=20/1100;
	$imgPath=getImgPath();
	$arPlay=getArrayPlay();
	ob_start();
	// echo nl2br(print_r($_REQUEST, true))."<br/>";
	// echo nl2br(print_r($_FILES, true))."<br/>";
	$fname=$_FILES['plfile']['name'];
	$base=pathinfo($fname,PATHINFO_BASENAME );
	$base=strtoupper(substr($base, 0, strrpos($base, '.')));
	// die('aqui '.$base);
	$nacos=explode('-',$base);
	if (count($nacos)==3){
		$user=strtoupper(trim($nacos[0]));
		$team=strtoupper(trim($nacos[1]));
		if (substr($team, 0, 4)=='INSO') $team='INSOMNIACS';
		$pos=$nacos[2];
		$pnacos=explode('X',$pos);
		if (count($pnacos)==2){
			$x=$pnacos[0];
			$y=$pnacos[1];
		}
	}elseif(count($nacos)==4){
		$user=strtoupper(trim($nacos[0]));
		$team=strtoupper(trim($nacos[1]));
		if (substr($team, 0, 4)=='INSO') $team='INSOMNIACS';
		$x=$nacos[2];
		$y=$nacos[3];
	}
	$ext=pathinfo($fname,PATHINFO_EXTENSION );

	if (!empty($_REQUEST['team'])) $team=trim($_REQUEST['team']);
	$map=$_REQUEST['map'];
	if (!empty($_REQUEST['x'])) $x=$_REQUEST['x'];
	if (!empty($_REQUEST['y'])) $y=$_REQUEST['y'];
	if (!empty($_REQUEST['user'])) $user=trim($_REQUEST['user']);
	$alliCod=null;
	if (!empty($team)){
		$alli=getAlliByName($team);
		if (!$alli){
			$alli=&FACTORY_lab::ALLIANCE();
			$alli->alli1_nam=strtoupper($team);
			$alli->alli1_mine=false;
			$alli->insert();
			$alliCod=$alli->alli1_cod;
		}else{
			$alliCod=$alli->alli1_cod;
		}
	}
	$playCod=null;
	$sql="select play1_cod from tb1_play where upper(trim(play1_nam))=upper(trim(?)) or (length(?)>3 and upper(trim(substring(play1_nam from 1 for length(?))))=upper(trim(?)))";
	if (!$rs=$db_lab->ExecuteBind($sql, [['varchar',$user],['varchar',$user],['varchar',$user],['varchar',$user]])){
		$db_lab->_raiseError(__FILE__,__LINE__,$sqlCompleto);
	}else{
		// echo nl2br(print_r($sqlCompleto, true))."<br/>";//exit;
		if (!$rs->EOF){
			$playCod=$rs->fields(0);
		}else{
			$play=&FACTORY_lab::PLAYER();
			$play->play1_nam=strtoupper($user);
			$play->alli1_cod=$alliCod;
			$play->insert();
			$playCod=$play->play1_cod;
		}
	}
	$s=getimagesize($_FILES['plfile']['tmp_name']);
	echo nl2br(print_r($s, true))."<br/>";
	echo $alliCod." allicod<br/>";
	echo $playCod." playcod<br/>";

	// verificação de sobreposição
	echo $fname." fname<br/>";
	$jaExiste=false;
	$planCod=null;
	$w=$s[0]*$fator;
	$h=$s[1]*$fator;
	$xmin=$x-$w/2/$esc;
	$xmax=$x+$w/2/$esc;
	$ymin=$y-$h/2/$esc;
	$ymax=$y+$h/2/$esc;
	$sql="select plan1_cod 
	from tb1_plan 
	where plan1_x between ? and ?
	and plan1_y between ? and ?";
	if (!$rs=$db_lab->ExecuteBind($sql, [['integer',$xmin],['integer',$xmax],['integer',$ymin],['integer',$ymax]])){
		$db_lab->_raiseError(__FILE__,__LINE__,$sqlCompleto);
	}else{
		// echo nl2br(print_r($sqlCompleto, true))."<br/>";
		if (!$rs->EOF){
			$planCod=$rs->fields(0);
			$jaExiste=true;
		}else{
			echo 'não achou'."<br/>";
		}
	}
	
	if ($jaExiste){
		//$msg='Este planeta já existe - '.$planCod.' (tratar sobreposição)';
		$plan=&FACTORY_lab::PLANET();
		$plan->loadByPk($planCod);
		$newFilename=$planCod.'.'.$ext;
		$dt=getdate();
		$renameFilename=$planCod.'-'.$dt['year'].$dt['mon'].$dt['mday'].$dt['hour'].$dt['minutes'].$dt['seconds'].'.'.$ext;
		$plan->alli1_cod=$alliCod;
		$plan->plan1_dat=time();
		$plan->plan1_img=$newFilename;
		$plan->plan1_map=$map;
		$plan->plan1_own=($playCod?$arPlay[$playCod]->play1_nam:strtoupper(trim($user)));
		if (!empty($playCod))$plan->play1_cod=$playCod;
		$plan->plan1_x=$x;
		$plan->plan1_y=$y;
		$plan->plan1_guns=$_REQUEST['guns'];
		$plan->plan1_lvl=$_REQUEST['level'];
		$plan->plan1_det=($_REQUEST['detection']=='true');
		$plan->plan1_jam=($_REQUEST['jammer']=='true');
		$plan->plan1_sens=($_REQUEST['sensor']=='true');
		$plan->plan1_repair=$_REQUEST['repair'];
		if ($plan->update()){
			rename($imgPath.$newFilename, $imgPath.$renameFilename);
			if (move_uploaded_file($_FILES['plfile']['tmp_name'], $imgPath.$newFilename)){
				// $db_lab->RollbackTrans();////////////////////////////////////////
				$db_lab->CommitTrans();
				$output=ob_get_clean();
				$resp=['output'=>$output,'status'=>true];
				$o=$plan->toMapJson($imgPath);
				// echo nl2br(print_r($resp, true))."<br/>";
?>
excludePlanet(<?=$planCod?>);
var resp=<?=json_encode($resp)?>;
$('#debugbox').html(resp.output);
addPlanet(<?=json_encode($o)?>);
doFilters();
updateStats();

<?
				exit;

			}else{
				//rollback
			}
		}else{
			//inform
		}
	}else{
		$seq = $db_lab->getSeq("TB1_PLAN", "PLAN1_COD");
		$newFilename=$seq.'.'.$ext;
		$plan=&FACTORY_lab::PLANET();
		$plan->alli1_cod=$alliCod;
		$plan->plan1_dat=time();
		$plan->plan1_cod=$seq;
		$plan->plan1_img=$newFilename;
		$plan->plan1_map=$map;
		$plan->plan1_own=($playCod?$arPlay[$playCod]->play1_nam:strtoupper(trim($user)));
		if (!empty($playCod))$plan->play1_cod=$playCod;
		$plan->plan1_x=$x;
		$plan->plan1_y=$y;
		$plan->plan1_guns=$_REQUEST['guns'];
		$plan->plan1_lvl=$_REQUEST['level'];
		$plan->plan1_det=($_REQUEST['detection']=='true');
		$plan->plan1_jam=($_REQUEST['jammer']=='true');
		$plan->plan1_sens=($_REQUEST['sensor']=='true');
		$plan->plan1_repair=$_REQUEST['repair'];
		// echo nl2br(print_r($plan, true))."<br/>";
		// exit;
		if (move_uploaded_file($_FILES['plfile']['tmp_name'], $imgPath.$newFilename)){
			if ($plan->hardInsert()){
				// $db_lab->RollbackTrans();////////////////////////////////////////
				$db_lab->CommitTrans();
				$output=ob_get_clean();
				$resp=['output'=>$output,'status'=>true];
				$o=$plan->toMapJson($imgPath);
				// echo nl2br(print_r($resp, true))."<br/>";
?>
var resp=<?=json_encode($resp)?>;
$('#debugbox').html(resp.output);
addPlanet(<?=json_encode($o)?>);
doFilters();
updateStats();
<?
				exit;
			}else{
				unlink( $imgPath.$newFilename);
				$msg='Error saving planet info';
				// $db_lab->RollbackTrans();////////////////////////////////////////
				$db_lab->CommitTrans();
				$output=ob_get_clean();
				$resp=['output'=>$output,'status'=>false,'msg'=>$msg];
				// echo nl2br(print_r($resp, true))."<br/>";
?>
				var resp=<?=json_encode($resp)?>;
				$('#debugbox').html(resp.output);
<?
				exit;
			}
		}else{
			$msg='Error storing planet image';
			// $db_lab->RollbackTrans();////////////////////////////////////////
			$db_lab->CommitTrans();
			$output=ob_get_clean();
			$resp=['output'=>$output,'status'=>false,'msg'=>$msg];
?>
			var resp=<?=json_encode($resp)?>;
			$('#debugbox').html(resp.output);
<?
			exit;
		}
	}
	// $db_lab->RollbackTrans();////////////////////////////////////////
	$db_lab->CommitTrans();
	$output=ob_get_clean();
	$resp=['output'=>$output,'status'=>false,'msg'=>$msg];
	// echo nl2br(print_r($resp, true))."<br/>";
?>
	var resp=<?=json_encode($resp)?>;
	$('#debugbox').html(resp.output);
<?
}
function newWhByClick(){
	global $db_lab, $sqlCompleto;
	ob_start();
	$msg='';
	$map=$_REQUEST['iew-map'];
	if (!empty($_REQUEST['iew-x'])) $x=$_REQUEST['iew-x'];
	if (!empty($_REQUEST['iew-y'])) $y=$_REQUEST['iew-y'];

	// verificação de sobreposição
	$jaExiste=false;
	$wrmhCod=null;
	$esc=900/1190;
	$fator=20/1100;
	$w=400*$fator;
	$h=400*$fator;
	$xmin=$x-$w/2/$esc;
	$xmax=$x+$w/2/$esc;
	$ymin=$y-$h/2/$esc;
	$ymax=$y+$h/2/$esc;
	$sql="select wrmh1_cod 
	from tb1_wrmh 
	where wrmh1_map=?
	and wrmh1_x between ? and ?
	and wrmh1_y between ? and ?";
	if (!$rs=$db_lab->ExecuteBind($sql, [['integer',$map],['integer',$xmin],['integer',$xmax],['integer',$ymin],['integer',$ymax]])){
		$db_lab->_raiseError(__FILE__,__LINE__,$sqlCompleto);
	}else{
		// echo nl2br(print_r($sqlCompleto, true))."<br/>";
		if (!$rs->EOF){
			$wrmhCod=$rs->fields(0);
			$jaExiste=true;
		}else{
			echo 'não achou'."<br/>";
		}
	}
	
	if ($jaExiste){
		//$msg='Este planeta já existe - '.$planCod.' (tratar sobreposição)';
		$wrmh=&FACTORY_lab::WORMHOLE();
		$wrmh->loadByPk($wrmhCod);
		$wrmh->wrmh1_x=$x;
		$wrmh->wrmh1_y=$y;
		$wrmh->wrmh1_maps=$_REQUEST['goodmaps'];
		if ($wrmh->update()){
			$output=ob_get_clean();
			$resp=['output'=>$output,'status'=>true];
			$o=$wrmh->toMapJson();
			// echo nl2br(print_r($resp, true))."<br/>";
?>
excludeWh(<?=$wrmhCod?>);
var resp=<?=json_encode($resp)?>;
$('#debugbox').html(resp.output);
addWh(<?=json_encode($o)?>);
<?
			exit;
		}else{
			//inform
		}
	}else{
		$wrmh=&FACTORY_lab::WORMHOLE();
		$wrmh->wrmh1_map=$map;
		if (!empty($wrmhCod))$wrmh->wrmh1_cod=$wrmhCod;
		$wrmh->wrmh1_x=$x;
		$wrmh->wrmh1_y=$y;
		$wrmh->wrmh1_map=$map;
		$wrmh->wrmh1_maps=$_REQUEST['goodmaps'];
		// echo nl2br(print_r($plan, true))."<br/>";
		// exit;
		if ($wrmh->insert()){
			$output=ob_get_clean();
			$resp=['output'=>$output,'status'=>true];
			$o=$wrmh->toMapJson();
			// echo nl2br(print_r($resp, true))."<br/>";
?>
var resp=<?=json_encode($resp)?>;
$('#debugbox').html(resp.output);
addWh(<?=json_encode($o)?>);
limpaEditWh();
<?
			exit;
		}else{
			$msg='Error storing planet image';
			$db_lab->RollbackTrans();
			// $db_lab->CommitTrans();
			$output=ob_get_clean();
			$resp=['output'=>$output,'status'=>false,'msg'=>$msg];
?>
			var resp=<?=json_encode($resp)?>;
			$('#debugbox').html(resp.output);
<?
			exit;
		}
	}
	$db_lab->RollbackTrans();
	// $db_lab->CommitTrans();
	$output=ob_get_clean();
	$resp=['output'=>$output,'status'=>false,'msg'=>$msg];
	// echo nl2br(print_r($resp, true))."<br/>";
?>
	var resp=<?=json_encode($resp)?>;
	$('#debugbox').html(resp.output);
<?
}

function newByClick(){
	global $db_lab, $sqlCompleto;
	$db_lab->BeginTrans();
	$esc=900/1190;
	$fator=20/1100;
	$imgPath=getImgPath();
	$arPlay=getArrayPlay();
	ob_start();
	$team=null;
	$msg='';
	if (!empty($_REQUEST['iep-team'])) $team=trim($_REQUEST['iep-team']);
	$map=$_REQUEST['iep-map'];
	if (!empty($_REQUEST['iep-x'])) $x=$_REQUEST['iep-x'];
	if (!empty($_REQUEST['iep-y'])) $y=$_REQUEST['iep-y'];
	$user=null;
	if (!empty($_REQUEST['iep-user'])) $user=trim($_REQUEST['iep-user']);
	$alliCod=null;
	if ($team){
		$alli=getAlliByName($team);
		if (!$alli){
			$alli=&FACTORY_lab::ALLIANCE();
			$alli->alli1_nam=strtoupper($team);
			$alli->alli1_mine=false;
			$alli->insert();
			$alliCod=$alli->alli1_cod;
		}else{
			$alliCod=$alli->alli1_cod;
		}
	}
	$playCod=null;
	$playIsNew=false;
	if ($user){
		if ($play=getPlayByName($user,$alliCod)){
			$playCod=$play->play1_cod;
		}else{
			$play=&FACTORY_lab::PLAYER();
			$play->play1_nam=strtoupper($user);
			$play->alli1_cod=$alliCod;
			$play->insert();
			$playCod=$play->play1_cod;
			$arPlay[$playCod]=$play;
			$playIsNew=true;
		}
	}
	echo $alliCod." allicod<br/>";
	echo $playCod." playcod<br/>";
	$shStatus=$_REQUEST['shields'];
	if ($shStatus=='null') {
		$shStatus=null;
	}elseif($shStatus=='true'){
		$shStatus=true;
	}elseif($shStatus=='false'){
		$shStatus=false;
	}else{
		$shStatus=null;
	}

	// verificação de sobreposição
	$jaExiste=false;
	$planCod=null;
	$w=400*$fator;
	$h=400*$fator;
	$xmin=$x-$w/2/$esc;
	$xmax=$x+$w/2/$esc;
	$ymin=$y-$h/2/$esc;
	$ymax=$y+$h/2/$esc;
	$sql="select plan1_cod 
	from tb1_plan 
	where plan1_x between ? and ?
	and plan1_y between ? and ?
	and plan1_map=?";
	if (!$rs=$db_lab->ExecuteBind($sql, [['integer',$xmin],['integer',$xmax],['integer',$ymin],['integer',$ymax],['integer',$map]])){
		$db_lab->_raiseError(__FILE__,__LINE__,$sqlCompleto);
	}else{
		// echo nl2br(print_r($sqlCompleto, true))."<br/>";
		if (!$rs->EOF){
			$planCod=$rs->fields(0);
			$jaExiste=true;
		}else{
			echo 'não achou'."<br/>";
		}
	}
	
	if ($jaExiste){
		//$msg='Este planeta já existe - '.$planCod.' (tratar sobreposição)';
		$plan=&FACTORY_lab::PLANET();
		$plan->loadByPk($planCod);
		$dt=getdate();
		$planFname=$plan->plan1_img;
		$renameFilename=str_replace($planCod, $planCod.'-'.$dt['year'].$dt['mon'].$dt['mday'].$dt['hour'].$dt['minutes'].$dt['seconds'],$planFname);
		$plan->plan1_img=null;
		// $plan->alli1_cod=$alliCod;
		$plan->plan1_dat=time();
		$plan->plan1_map=$map;
		$plan->plan1_own=($playCod?$arPlay[$playCod]->play1_nam:(empty($user)?'UNKNOWN':strtoupper(trim($user))));
		if (!empty($playCod))$plan->play1_cod=$playCod;
		$plan->plan1_x=$x;
		$plan->plan1_y=$y;
		$plan->plan1_guns=$_REQUEST['guns'];
		$plan->plan1_lvl=$_REQUEST['level'];
		$plan->plan1_det=($_REQUEST['detection']=='true');
		$plan->plan1_jam=($_REQUEST['jammer']=='true');
		$plan->plan1_sens=($_REQUEST['sensor']=='true');
		$plan->plan1_repair=$_REQUEST['repair'];
		$statusAtual=$plan->plan1_shl;
		$plan->plan1_shl=$shStatus;
		if (is_null($shStatus)) {
			$plan->plan1_ts_shield=null;
		}elseif ($statusAtual!==$shStatus){
			$plan->plan1_ts_shield=time();
		}elseif(!empty($plan->plan1_ts_shield) && $plan->plan1_ts_shield+72*60*60<time()){
			$plan->plan1_ts_shield=null;
		}
		if ($plan->update()){
			rename($imgPath.$planFname, $imgPath.$renameFilename);
			// $db_lab->RollbackTrans();////////////////////////////////////////
			$db_lab->CommitTrans();
			$output=ob_get_clean();
			$resp=['output'=>$output,'status'=>true];
			$o=$plan->toMapJson($imgPath);
			// echo nl2br(print_r($resp, true))."<br/>";
?>
players[<?=$playCod?>]={'name':'<?=$play->play1_nam?>','alli':'<?=$play->alli1_cod?>'};
excludePlanet(<?=$planCod?>);
var resp=<?=json_encode($resp)?>;
$('#debugbox').html(resp.output);
addPlanet(<?=json_encode($o)?>);
doFilters();
updateStats();
<?
			exit;
		}else{
			//inform
		}
	}else{
		$plan=&FACTORY_lab::PLANET();
		// $plan->alli1_cod=$alliCod;
		$plan->plan1_dat=time();
		$plan->plan1_img=null;
		$plan->plan1_map=$map;
		$plan->map1_cod=$map;
		$plan->plan1_own=($playCod?$arPlay[$playCod]->play1_nam:strtoupper(trim($user)));
		if (!empty($playCod))$plan->play1_cod=$playCod;
		$plan->plan1_x=$x;
		$plan->plan1_y=$y;
		$plan->plan1_guns=$_REQUEST['guns'];
		$plan->plan1_lvl=$_REQUEST['level'];
		$plan->plan1_det=($_REQUEST['detection']=='true');
		$plan->plan1_jam=($_REQUEST['jammer']=='true');
		$plan->plan1_sens=($_REQUEST['sensor']=='true');
		$plan->plan1_repair=$_REQUEST['repair'];
		$statusAtual=$plan->plan1_shl;
		$plan->plan1_shl=$shStatus;
		if (is_null($shStatus)) {
			$plan->plan1_ts_shield=null;
		}elseif ($statusAtual!==$shStatus){
			$plan->plan1_ts_shield=time();
		}elseif(!empty($plan->plan1_ts_shield) && $plan->plan1_ts_shield+72*60*60<time()){
			$plan->plan1_ts_shield=null;
		}
			// echo nl2br(print_r($plan, true))."<br/>";
		// exit;
		if ($plan->insert()){
			// $db_lab->RollbackTrans();////////////////////////////////////////
			$db_lab->CommitTrans();
			$output=ob_get_clean();
			$resp=['output'=>$output,'status'=>true];
			$o=$plan->toMapJson($imgPath);
			// echo nl2br(print_r($resp, true))."<br/>";
?>
players[<?=$playCod?>]={'name':'<?=$play->play1_nam?>','alli':'<?=$play->alli1_cod?>'};
var resp=<?=json_encode($resp)?>;
$('#debugbox').html(resp.output);
addPlanet(<?=json_encode($o)?>);
doFilters();
updateStats();
limpaEditPl();
<?
			exit;
		}else{
			$msg='Error storing planet image';
			$db_lab->RollbackTrans();
			// $db_lab->CommitTrans();
			$output=ob_get_clean();
			$resp=['output'=>$output,'status'=>false,'msg'=>$msg];
?>
			var resp=<?=json_encode($resp)?>;
			$('#debugbox').html(resp.output);
<?
			exit;
		}
	}
	$db_lab->RollbackTrans();
	// $db_lab->CommitTrans();
	$output=ob_get_clean();
	$resp=['output'=>$output,'status'=>false,'msg'=>$msg];
	// echo nl2br(print_r($resp, true))."<br/>";
?>
	var resp=<?=json_encode($resp)?>;
	$('#debugbox').html(resp.output);
<?
}
function infoEditWh(){
	$id=$_REQUEST['id'];
	$wrmh=&FACTORY_lab::WORMHOLE();
	if(!$wrmh->loadByPk($id)) die('{"status":false}');
	$o=[
		'id'=>$id,
		'map'=>$wrmh->wrmh1_map,
		'x'=>$wrmh->wrmh1_x,
		'y'=>$wrmh->wrmh1_y,
		'maps'=>$wrmh->wrmh1_maps,
	];
	echo json_encode($o);
	exit;
}
function updateWh(){
	$id=$_REQUEST['id'];
	$wrmh=&FACTORY_lab::WORMHOLE();
	if(!$wrmh->loadByPk($id)) die('{"status":false}');//////////////////////////
	$wrmh->wrmh1_x=$_REQUEST['iew-x'];
	$wrmh->wrmh1_y=$_REQUEST['iew-y'];
	$wrmh->wrmh1_map=$_REQUEST['iew-map'];
	$wrmh->wrmh1_maps=$_REQUEST['goodmaps'];
	if ($wrmh->update()){
		$o=$wrmh->toMapJson();
		?>$('#iew-id').val('');$('#infoEditWh').hide();excludeWh(<?=$id?>);addWh(<?=json_encode($o)?>);<?
	}else{
		?>alert('Error updating wormhole')<?
	}
}
function infoEditPl(){
	$id=$_REQUEST['id'];
	$plan=&FACTORY_lab::PLANET();
	if(!$plan->loadByPk($id)) die('{"status":false}');
	$play=$plan->getPLAYER();
	$alli=$plan->getALLIANCE();
	$o=[
		'status'=>true,
		'id'=>$id,
		'user'=>$play->play1_nam,
		'uid'=>$play->play1_cod,
		'team'=>($alli?$alli->alli1_nam:''),
		'map'=>$plan->plan1_map,
		'x'=>$plan->plan1_x,
		'y'=>$plan->plan1_y,
		'guns'=>$plan->plan1_guns,
		'level'=>$plan->plan1_lvl,
		'detection'=>$plan->plan1_det,
		'jammer'=>$plan->plan1_jam,
		'sensor'=>$plan->plan1_sens,
		'repair'=>$plan->plan1_repair,
		'shl'=>$plan->plan1_shl
	];
	if (!empty($plan->plan1_ts_shield)) {
		if ($plan->plan1_ts_shield+72*60*60>time()) {
			$o['sh']=$plan->plan1_ts_shield*1000;
		}else {
			$o['sh']=null;
			$o['shl']=null;
			$plan->plan1_ts_shield=null;
			$plan->update();
		}
	}else{
		$o['shl']=null;
		$o['sh']=null;
	}
	echo json_encode($o);
	exit;
}
function updatePl(){
	$id=$_REQUEST['id'];
	$plan=&FACTORY_lab::PLANET();
	if(!$plan->loadByPk($id)) die('{"status":false}');//////////////////////////

	$user=$_REQUEST['iep-user'];
	$team=(isset($_REQUEST['iep-team'])?$_REQUEST['iep-team']:null);
	$x=$_REQUEST['iep-x'];
	$y=$_REQUEST['iep-y'];
	$shStatus=$_REQUEST['shields'];
	if ($shStatus=='null') {
		$shStatus=null;
	}elseif($shStatus=='true' || $shStatus===true){
		$shStatus=true;
	}elseif($shStatus=='false' || $shStatus===false){
		$shStatus=false;
	}else{
		$shStatus=null;
	}
	// var_dump($shStatus);
	$alliCod=null;
	$playCod=null;
	if ($team && $alli=getAlliByName($team)){
		$alliCod=$alli->alli1_cod;
	}
	if ($play=getPlayByName($user,$alliCod)){
		$playCod=$play->play1_cod;
		// echo nl2br(print_r($play, true))."+++<br/>";exit;
	}else{
		$play=&FACTORY_lab::PLAYER();
		$play->play1_nam=strtoupper($user);
		$play->alli1_cod=$alliCod;
		$play->insert();
		$playCod=$play->play1_cod;
	}
	// echo nl2br(print_r($play, true))."===<br/>";exit;
	if ($alliCod || empty($team)) $plan->alli1_cod=$alliCod;
	$origPlay=$plan->play1_cod;
	if ($playCod) $plan->play1_cod=$playCod;
	$plan->plan1_x=$x;
	$plan->plan1_y=$y;
	$plan->plan1_dat=time();
	$plan->plan1_guns=$_REQUEST['guns'];
	$plan->plan1_lvl=$_REQUEST['level'];
	$plan->plan1_det=($_REQUEST['detection']=='true');
	$plan->plan1_jam=($_REQUEST['jammer']=='true');
	$plan->plan1_sens=($_REQUEST['sensor']=='true');
	$plan->plan1_repair=$_REQUEST['repair'];
	$statusAtual=$plan->plan1_shl;
	$plan->plan1_shl=$shStatus;
	if (is_null($shStatus)) {
		$plan->plan1_ts_shield=null;
	}elseif ($statusAtual!==$shStatus || $origPlay!=$playCod){
		$plan->plan1_ts_shield=time();
	}elseif(!empty($plan->plan1_ts_shield) && $plan->plan1_ts_shield+72*60*60<time()){
		$plan->plan1_ts_shield=null;
	}elseif ($shStatus===false || $shStatus===true){
		$plan->plan1_ts_shield=time();
	}
	
	// echo nl2br(print_r($plan, true))."<br/>";exit;
	if ($plan->update()){
		// var_dump($plan->plan1_shl);
		// echo nl2br(print_r($plan, true))."<br/>";
		$o=$plan->toMapJson();
		?>$('#iep-id').val('');$('#infoEditPl').hide();excludePlanet(<?=$id?>);addPlanet(<?=json_encode($o)?>);updateStats();<?
	}else{
		?>alert('Error updating planet')<?
	}
}
function addShields(){
	$id=$_REQUEST['cod'];
	$plan=&FACTORY_lab::PLANET();
	if(!$plan->loadByPk($id)) die("alert('Planet not found')");
	$plan->plan1_shl=true;
	$plan->plan1_ts_shield=time();
	if (!$plan->update()) die("alert('Error updating planet')");
	$o=$plan->toMapJson();
	?>excludePlanet(<?=$id?>);addPlanet(<?=json_encode($o)?>);<?
}
function removeShields(){
	$id=$_REQUEST['cod'];
	$plan=&FACTORY_lab::PLANET();
	if(!$plan->loadByPk($id)) die("alert('Planet not found')");
	$plan->plan1_shl=false;
	$plan->plan1_ts_shield=time();
	if (!$plan->update()) die("alert('Error updating planet')");
	$o=$plan->toMapJson();
	?>excludePlanet(<?=$id?>);addPlanet(<?=json_encode($o)?>);<?
}
function unknownShields(){
	$id=$_REQUEST['cod'];
	$plan=&FACTORY_lab::PLANET();
	if(!$plan->loadByPk($id)) die("alert('Planet not found')");
	$plan->plan1_shl=null;
	$plan->plan1_ts_shield=null;
	if (!$plan->update()) die("alert('Error updating planet')");
	$o=$plan->toMapJson();
	?>excludePlanet(<?=$id?>);addPlanet(<?=json_encode($o)?>);<?
}
function doEditPlay(){
	$playCod=$_REQUEST['play'];
	$name=$_REQUEST['name'];
	$name=trim($name);
	$alliCod=$_REQUEST['alli'];
	if (empty($alliCod) || $alliCod=='0') $alliCod=null;
	$play=&FACTORY_lab::PLAYER();
	if (!$play->loadByPk($playCod)) die("alert('Player not found')");
	$play->play1_nam=$name;
	$play->alli1_cod=$alliCod;
	if (!$play->update()) die("alert('Error updating player')");
	?>document.location.reload();<?
}
function doNewAlliance(){
	$name=$_REQUEST['name'];
	$alli=&FACTORY_lab::ALLIANCE();
	if (empty(trim($name))) die("alert('Alliance name cannot be empty')");
	if ($alli->getByName($name)) die("alert('An alliance with this name already exists')");
	$alli->alli1_nam=strtoupper($name);
	$alli->alli1_mine=false;
	if (!$alli->insert()) die("alert('Error saving alliance')");
}
function delWh(){
	$id=$_REQUEST['cod'];
	$wrmh=&FACTORY_lab::WORMHOLE();
	if(!$wrmh->loadByPk($id)) die("alert('Wormhole not found')");
	if (!$wrmh->delete()) die("alert('Error deleting wormhole')");
	die("excludeWh(".$id.");limpaEditWh()");
}
function delPlan(){
	$id=$_REQUEST['cod'];
	$plan=&FACTORY_lab::PLANET();
	if(!$plan->loadByPk($id)) die("alert('Planet not found')");
	if (!$plan->delete()) die("alert('Error deleting Planet')");
	die("excludePlanet(".$id.");limpaEditPl()");
}
function formEditPlay(){
	$arAlli=getArrayAlli();
?>
<div id="formEditPlay" class="content" style="min-height:50px;>
	<div class="row">
		<div class="col-sm-12">
			<div class="form-group">
				<label>Player</label>
				<input class="form-control" type="text" name="ep-play1_nam" id="ep-play1_nam" value="" placeholder="Player name"/>
			</div>
			<div class="form-group">
				<label>Alliance</label>
				<select class="form-control" name="ep-alli1_cod" id="ep-alli1_cod">
					<option value="">Select</option>
					<option value="0">No alliance</option>
<?
foreach($arAlli as $alliCod=>$alli){
?>
					<option value="<?=$alliCod?>"><?=$alli->alli1_nam?></option>
<?
}
?>
				</select>
			</div>
		</div>
	</div>
</div>
<?
}
function showWhFromMap(){
	$mapN=$_REQUEST['map'];
	$wrmhList=&FACTORY_lab::WORMHOLE_LIST();
	$sql="select ".$wrmhList->getAster()."
	from tb1_wrmh
	where wrmh1_map=?";
	$wrmhList->loadBySQL($sql, [['integer',$mapN]]);
	$arWh=[];
	while($wrmh=$wrmhList->getNextItem()){
		$arWh[]=$wrmh->toMapJson();
	}
	echo json_encode($arWh);
}
function impUser(){
	global $language;
	if (!empty($_REQUEST['lang'])) $language=$_REQUEST['lang'];
	if (empty($language)) $language='enus';
	$atual=$_REQUEST['user'];
	$real=$_REQUEST['ruc'];
	$playList=&FACTORY_lab::PLAYER_LIST();
	$playList->getTodos('order by play1_nam');
	$alliList=&FACTORY_lab::ALLIANCE_LIST();
	$alliList->getTodos('order by alli1_nam');
	$arAlli=$alliList->toArray();
?>
<div class="content" style="min-height:50px;">
	<div class="row">
		<div class="col-sm-12">
			<div class="form-group">
				<label>user</label>
				<select class="form-control" name="impUser" id="impUser">
					<option value="">Selecione</option>
<?
	while($play=$playList->getNextItem()){
		if (empty($play->play1_nam)) continue;
		$alliname='No alliance';
		if (!empty($play->alli1_cod)) $alliname=$arAlli[$play->alli1_cod]->alli1_nam;
?>
					<option value="<?=$play->play1_cod?>"<?=$play->play1_cod==$atual?' selected':''?>><?=$play->play1_nam.' ('.$alliname.')'?></option>
<?
	}
?>
				</select>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-sm-4 text-left">
			<button class="btn btn-default" onclick="$('#impUserDialog').dialog('close').dialog('destroy').remove();"><?=lang('close')?></button>
		</div>
		<div class="col-sm-4 text-center">
			<button class="btn btn-default" onclick="document.location='index.php?map='+mapN+'&lang='+lang;"<?=$real==$atual?' disabled':''?>><?=lang('clear')?></button>
		</div>
		<div class="col-sm-4 text-right">
			<button class="btn btn-primary" onclick="document.location='index.php?viewas='+$('#impUser').val()+'&map='+mapN+'&lang='+lang;"><?=lang('viewas')?></button>
		</div>
	</div>
</div>
<?
}

function uploadMap(){
	global $db_lab, $sqlCompleto;
	$db_lab->BeginTrans();

	try {
		if (!isset($_FILES['mapfile']) || $_FILES['mapfile']['error'] !== UPLOAD_ERR_OK) {
			throw new Exception('No file uploaded or upload error');
		}

		$uploadedFile = $_FILES['mapfile']['tmp_name'];

		// Load the uploaded image
		$imageInfo = getimagesize($uploadedFile);
		if (!$imageInfo) {
			throw new Exception('Invalid image file');
		}

		// Create image resource based on type
		switch ($imageInfo[2]) {
			case IMAGETYPE_JPEG:
				$image = imagecreatefromjpeg($uploadedFile);
				break;
			case IMAGETYPE_PNG:
				$image = imagecreatefrompng($uploadedFile);
				break;
			case IMAGETYPE_GIF:
				$image = imagecreatefromgif($uploadedFile);
				break;
			default:
				throw new Exception('Unsupported image format');
		}

		if (!$image) {
			throw new Exception('Failed to create image resource');
		}

		// Get image dimensions
		$width = imagesx($image);
		$height = imagesy($image);

		// Detect where the HTML document window starts (after browser chrome)
		$documentStartY = detectDocumentWindowStart($image, $width, $height);

		// Extract mini-map from top-right corner (approximately 300x300 pixels)
		$miniMapSize = 300;
		$miniMapX = $width - $miniMapSize;
		$miniMapY = $documentStartY;

		// Create mini-map image
		$miniMap = imagecreatetruecolor($miniMapSize, $miniMapSize);
		imagecopy($miniMap, $image, 0, 0, $miniMapX, $miniMapY, $miniMapSize, $miniMapSize);
		// imagepng($miniMap, 'mini_map.png');
		// Extract map number from text below mini-map
		$mapN = extractMapNumberFromImage($image, $width, $height, $miniMapX, $miniMapY, $miniMapSize);
		if (!$mapN) {
			// Fallback to current map number from request, or default
			$mapN = $_REQUEST['currentMap'] ?? $_REQUEST['map'] ?? 468;
		}

		// Process mini-map to detect planets
		$detectedPlanets = detectPlanetsFromMiniMap($miniMap, $miniMapSize);

		// Clean up image resources
		imagedestroy($image);
		imagedestroy($miniMap);

		// Insert new planets into database
		$newPlanetsCount = 0;
		$map=&FACTORY_lab::MAP();
		if (!$map->loadByPk($mapN)){
			$map->map1_cod=$mapN;
			$map->insert();
		}
		foreach ($detectedPlanets as $planet) {
			// Check if planet already exists at this location
			$sql = "SELECT plan1_cod FROM tb1_plan WHERE plan1_map = ? AND plan1_x = ? AND plan1_y = ?";
			$rs = $db_lab->ExecuteBind($sql, [
				['integer', $mapN],
				['integer', $planet['x']],
				['integer', $planet['y']]
			]);

			if ($rs && $rs->EOF) {
				// Planet doesn't exist, create new one
				$plan = &FACTORY_lab::PLANET();
				$plan->map1_cod = $mapN;
				$plan->plan1_map = $mapN;
				$plan->plan1_x = $planet['x'];
				$plan->plan1_y = $planet['y'];
				$plan->plan1_lvl = -1; // Mark as incomplete
				$plan->play1_cod = null; // Unknown player
				$plan->plan1_guns = 2;
				$plan->plan1_det = false;
				$plan->plan1_jam = false;
				$plan->plan1_sens = false;
				$plan->plan1_repair = null;
				$plan->plan1_shl = null;
				$plan->plan1_dat = time();

				if ($plan->insert()) {
					$newPlanetsCount++;
				}
			}
		}

		$db_lab->CommitTrans();

		// Return success response
		echo "alert('Map processed successfully! Detected Map $mapN and added $newPlanetsCount new planets.'); window.location.reload();";

	} catch (Exception $e) {
		$db_lab->RollbackTrans();
		echo "alert('Error processing map: " . addslashes($e->getMessage()) . "');";
	}
}

function detectPlanetsFromMiniMap($miniMap, $size) {
	$planets = [];

	// First, find all bright pixels that could be part of planets
	$brightPixels = findBrightPixels($miniMap, $size);

	// Group bright pixels into potential planet clusters
	$clusters = clusterBrightPixels($brightPixels);

	// For each cluster, find the center (planet center)
	foreach ($clusters as $cluster) {
		$center = findClusterCenter($cluster);
		if ($center && isValidPlanetCluster($cluster, $center)) {
			// Convert mini-map coordinates to game coordinates
			// Assuming mini-map represents a 1200x1200 game area
			$gameX = round(($center['x'] / $size) * 1200);
			$gameY = round(($center['y'] / $size) * 1200);
			ec
			$planets[] = [
				'x' => $gameX,
				'y' => $gameY,
				'miniX' => $center['x'],
				'miniY' => $center['y'],
				'clusterSize' => count($cluster),
				'avgBrightness' => $center['avgBrightness']
			];
		}
	}

	return $planets;
}

function findBrightPixels($miniMap, $size) {
	$brightPixels = [];
	$threshold = 5; // Lower threshold for better planet detection

	// Scan entire mini-map for bright pixels
	for ($x = 2; $x < $size - 2; $x++) {
		for ($y = 2; $y < $size - 2; $y++) {
			$rgb = imagecolorat($miniMap, $x, $y);
			$r = ($rgb >> 16) & 0xFF;
			$g = ($rgb >> 8) & 0xFF;
			$b = $rgb & 0xFF;

			// Calculate brightness
			$brightness = ($r + $g + $b) / 3;

			// Check if pixel is bright enough to be part of a planet
			if ($brightness > $threshold) {
				$brightPixels[] = [
					'x' => $x,
					'y' => $y,
					'brightness' => $brightness
				];
			}
		}
	}

	return $brightPixels;
}

function clusterBrightPixels($brightPixels) {
	$clusters = [];
	$used = [];
	$clusterRadius = 4; // Maximum distance for pixels to be in same cluster

	foreach ($brightPixels as $i => $pixel) {
		if (isset($used[$i])) continue;

		$cluster = [$pixel];
		$used[$i] = true;

		// Find all nearby pixels that belong to this cluster
		foreach ($brightPixels as $j => $otherPixel) {
			if (isset($used[$j])) continue;

			$distance = sqrt(pow($pixel['x'] - $otherPixel['x'], 2) + pow($pixel['y'] - $otherPixel['y'], 2));
			if ($distance <= $clusterRadius) {
				$cluster[] = $otherPixel;
				$used[$j] = true;
			}
		}

		// Only keep clusters with enough pixels to be a planet
		if (count($cluster) >= 3) {
			$clusters[] = $cluster;
		}
	}

	return $clusters;
}

function findClusterCenter($cluster) {
	if (empty($cluster)) return null;

	$sumX = 0;
	$sumY = 0;
	$sumBrightness = 0;
	$count = count($cluster);

	// Calculate weighted center based on brightness
	foreach ($cluster as $pixel) {
		$weight = $pixel['brightness'];
		$sumX += $pixel['x'] * $weight;
		$sumY += $pixel['y'] * $weight;
		$sumBrightness += $weight;
	}

	if ($sumBrightness == 0) return null;

	return [
		'x' => round($sumX / $sumBrightness),
		'y' => round($sumY / $sumBrightness),
		'avgBrightness' => $sumBrightness / $count
	];
}

function isValidPlanetCluster($cluster, $center) {
	$count = count($cluster);

	// Must have minimum number of pixels
	if ($count < 3) return false;

	// Must not have too many pixels (avoid large bright areas)
	if ($count > 50) return false;

	// Check if cluster has roughly circular shape
	$maxDistance = 0;
	$minDistance = PHP_FLOAT_MAX;

	foreach ($cluster as $pixel) {
		$distance = sqrt(pow($pixel['x'] - $center['x'], 2) + pow($pixel['y'] - $center['y'], 2));
		$maxDistance = max($maxDistance, $distance);
		$minDistance = min($minDistance, $distance);
	}

	// Reject if cluster is too elongated (not circular enough)
	if ($maxDistance > 0 && ($minDistance / $maxDistance) < 0.3) {
		return false;
	}

	// Must have reasonable size for a planet (2-8 pixel radius)
	if ($maxDistance < 1 || $maxDistance > 8) {
		return false;
	}

	return true;
}

function extractMapNumberFromImage($image, $imageWidth, $height, $miniMapX, $miniMapY, $miniMapSize) {
	// Define the area below the mini-map where the map number text should be
	$textAreaX = $miniMapX;
	$textAreaY = $miniMapY + $miniMapSize + 5; // 5px below mini-map
	$textAreaWidth = $miniMapSize;
	$textAreaHeight = 30; // Height for text area

	// Make sure we don't go beyond image boundaries
	if ($textAreaY + $textAreaHeight > $height) {
		$textAreaHeight = $height - $textAreaY;
	}
	if ($textAreaHeight <= 0) {
		return null;
	}

	// Create image for the text area
	$textImage = imagecreatetruecolor($textAreaWidth, $textAreaHeight);
	imagecopy($textImage, $image, 0, 0, $textAreaX, $textAreaY, $textAreaWidth, $textAreaHeight);

	// Try OCR first if Tesseract is available
	$mapNumber = tryOCRExtraction($textImage);
	if ($mapNumber) {
		imagedestroy($textImage);
		return $mapNumber;
	}

	// Fallback to pixel analysis
	imagefilter($textImage, IMG_FILTER_GRAYSCALE);
	imagefilter($textImage, IMG_FILTER_CONTRAST, -50);

	$mapNumber = analyzeMapNumberPixels($textImage, $textAreaWidth, $textAreaHeight);

	imagedestroy($textImage);
	return $mapNumber;
}

function tryOCRExtraction($textImage) {
	// Save temporary image for OCR
	$tempFile = tempnam(sys_get_temp_dir(), 'map_ocr_') . '.png';
	imagepng($textImage, $tempFile);

	try {
		// Try using Tesseract OCR if available
		if (function_exists('exec')) {
			$output = [];
			$returnCode = 0;

			// Try tesseract command
			exec("tesseract \"$tempFile\" stdout -c tessedit_char_whitelist=MAP0123456789 2>/dev/null", $output, $returnCode);

			if ($returnCode === 0 && !empty($output)) {
				$text = implode(' ', $output);
				$mapNumber = extractMapNumberFromText($text);
				if ($mapNumber) {
					unlink($tempFile);
					return $mapNumber;
				}
			}
		}
	} catch (Exception $e) {
		// OCR failed, continue with pixel analysis
	}

	unlink($tempFile);
	return null;
}

function extractMapNumberFromText($text) {
	// Look for MAP followed by numbers
	if (preg_match('/MAP\s*(\d{3})/i', $text, $matches)) {
		return intval($matches[1]);
	}

	// Look for just 3-digit numbers
	if (preg_match('/(\d{3})/', $text, $matches)) {
		$number = intval($matches[1]);
		if ($number >= 100 && $number <= 999) {
			return $number;
		}
	}

	return null;
}

function analyzeMapNumberPixels($textImage, $width, $height) {
	// This is a simplified approach to detect "MAP" followed by numbers
	// We'll look for patterns that might represent text

	$brightPixels = [];
	$threshold = 200; // Brightness threshold for text detection

	// Scan for bright pixels (text is usually bright on dark background)
	for ($y = 0; $y < $height; $y++) {
		for ($x = 0; $x < $width; $x++) {
			$rgb = imagecolorat($textImage, $x, $y);
			$r = ($rgb >> 16) & 0xFF;
			$g = ($rgb >> 8) & 0xFF;
			$b = $rgb & 0xFF;
			$brightness = ($r + $g + $b) / 3;

			if ($brightness > $threshold) {
				$brightPixels[] = ['x' => $x, 'y' => $y, 'brightness' => $brightness];
			}
		}
	}

	// If we have enough bright pixels, try to find patterns
	if (count($brightPixels) < 20) {
		return null; // Not enough text-like pixels
	}

	// Group pixels by horizontal lines (text rows)
	$textRows = [];
	foreach ($brightPixels as $pixel) {
		$rowKey = intval($pixel['y'] / 3) * 3; // Group by 3-pixel rows
		if (!isset($textRows[$rowKey])) {
			$textRows[$rowKey] = [];
		}
		$textRows[$rowKey][] = $pixel;
	}

	// Find the row with most pixels (likely the main text line)
	$mainRow = null;
	$maxPixels = 0;
	foreach ($textRows as $rowY => $pixels) {
		if (count($pixels) > $maxPixels) {
			$maxPixels = count($pixels);
			$mainRow = $pixels;
		}
	}

	if (!$mainRow || count($mainRow) < 15) {
		return null; // Not enough pixels in main text row
	}

	// Sort pixels by X coordinate
	usort($mainRow, function($a, $b) {
		return $a['x'] - $b['x'];
	});

	// Try to detect number patterns in the pixel arrangement
	// This is a very basic approach - in a real implementation you'd use OCR
	$pixelGroups = groupConsecutivePixels($mainRow);

	// Look for 3-6 character groups that might represent "MAP" + numbers
	foreach ($pixelGroups as $group) {
		if (count($group) >= 15 && count($group) <= 40) {
			// Try to extract numbers from the rightmost part of the group
			$numbers = extractNumbersFromPixelGroup($group);
			if ($numbers && $numbers >= 100 && $numbers <= 999) {
				return $numbers;
			}
		}
	}

	return null;
}

function groupConsecutivePixels($pixels) {
	$groups = [];
	$currentGroup = [];
	$lastX = -10;

	foreach ($pixels as $pixel) {
		if ($pixel['x'] - $lastX <= 3) { // Pixels within 3 units are part of same group
			$currentGroup[] = $pixel;
		} else {
			if (!empty($currentGroup)) {
				$groups[] = $currentGroup;
			}
			$currentGroup = [$pixel];
		}
		$lastX = $pixel['x'];
	}

	if (!empty($currentGroup)) {
		$groups[] = $currentGroup;
	}

	return $groups;
}

function extractNumbersFromPixelGroup($group) {
	// This is a very simplified number extraction
	// In practice, you'd use proper OCR or template matching

	$width = max(array_column($group, 'x')) - min(array_column($group, 'x'));
	$pixelCount = count($group);

	// Based on pixel density and width, try to guess the number
	// This is very approximate and would need refinement
	if ($width >= 20 && $width <= 60 && $pixelCount >= 15) {
		// Try some common map numbers based on pixel patterns
		$commonMaps = [468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480];

		// For now, return a default that can be overridden
		// In a real implementation, you'd analyze the actual pixel patterns
		return 468; // Default fallback
	}

	return null;
}

function detectDocumentWindowStart($image, $width, $height) {
	// Browser chrome typically has distinct color patterns
	// Look for horizontal lines that indicate the start of the document area

	$maxScanHeight = min(200, $height); // Don't scan more than 200px from top
	$documentStartY = 0;

	// Sample multiple points across the width to detect browser chrome
	$samplePoints = 10;
	$stepX = $width / $samplePoints;

	// Look for significant color changes that indicate document start
	for ($y = 10; $y < $maxScanHeight; $y++) {
		$colorChanges = 0;
		$avgBrightness = 0;
		$prevBrightness = null;

		// Sample across the width at this Y position
		for ($i = 0; $i < $samplePoints; $i++) {
			$x = round($i * $stepX);
			if ($x >= $width) $x = $width - 1;

			$rgb = imagecolorat($image, $x, $y);
			$r = ($rgb >> 16) & 0xFF;
			$g = ($rgb >> 8) & 0xFF;
			$b = $rgb & 0xFF;
			$brightness = ($r + $g + $b) / 3;

			$avgBrightness += $brightness;

			// Check for significant brightness changes
			if ($prevBrightness !== null) {
				$diff = abs($brightness - $prevBrightness);
				if ($diff > 30) { // Significant color change
					$colorChanges++;
				}
			}
			$prevBrightness = $brightness;
		}

		$avgBrightness /= $samplePoints;

		// Look for patterns that indicate document content area
		if (isDocumentContentRow($image, $width, $y, $avgBrightness, $colorChanges)) {
			$documentStartY = $y;
			break;
		}
	}

	// Fallback: look for common browser chrome heights
	if ($documentStartY == 0) {
		$documentStartY = detectBrowserChromeHeight($image, $width, $height);
	}

	return $documentStartY;
}

function isDocumentContentRow($image, $width, $y, $avgBrightness, $colorChanges) {
	// Document content typically has:
	// 1. More color variation (colorChanges > threshold)
	// 2. Different brightness patterns than browser chrome
	// 3. Horizontal patterns that suggest UI elements

	// Check if this row has characteristics of document content
	if ($colorChanges >= 3) { // Multiple color changes suggest content
		// Additional validation: check for horizontal patterns
		$horizontalPatterns = detectHorizontalPatterns($image, $width, $y);
		if ($horizontalPatterns > 2) {
			return true;
		}
	}

	// Check for specific brightness patterns that suggest document area
	if ($avgBrightness > 100 && $avgBrightness < 200) { // Typical document background
		// Look for edge patterns that suggest UI elements
		$edgeCount = countEdges($image, $width, $y);
		if ($edgeCount > 5) {
			return true;
		}
	}

	return false;
}

function detectHorizontalPatterns($image, $width, $y) {
	$patterns = 0;
	$prevColor = null;
	$segmentLength = 0;
	$minSegmentLength = 10; // Minimum length for a pattern segment

	for ($x = 0; $x < $width; $x++) {
		$rgb = imagecolorat($image, $x, $y);

		if ($prevColor === null) {
			$prevColor = $rgb;
			$segmentLength = 1;
		} elseif (abs($rgb - $prevColor) < 1000) { // Similar color
			$segmentLength++;
		} else {
			// Color change detected
			if ($segmentLength >= $minSegmentLength) {
				$patterns++;
			}
			$prevColor = $rgb;
			$segmentLength = 1;
		}
	}

	// Check final segment
	if ($segmentLength >= $minSegmentLength) {
		$patterns++;
	}

	return $patterns;
}

function countEdges($image, $width, $y) {
	$edges = 0;
	$threshold = 30; // Brightness difference threshold for edge detection

	for ($x = 1; $x < $width - 1; $x++) {
		$rgb1 = imagecolorat($image, $x - 1, $y);
		$rgb2 = imagecolorat($image, $x + 1, $y);

		$r1 = ($rgb1 >> 16) & 0xFF;
		$g1 = ($rgb1 >> 8) & 0xFF;
		$b1 = $rgb1 & 0xFF;
		$brightness1 = ($r1 + $g1 + $b1) / 3;

		$r2 = ($rgb2 >> 16) & 0xFF;
		$g2 = ($rgb2 >> 8) & 0xFF;
		$b2 = $rgb2 & 0xFF;
		$brightness2 = ($r2 + $g2 + $b2) / 3;

		if (abs($brightness1 - $brightness2) > $threshold) {
			$edges++;
		}
	}

	return $edges;
}

function detectBrowserChromeHeight($image, $width, $height) {
	// Common browser chrome heights for different browsers and OS
	$commonHeights = [
		80,  // Chrome/Edge with bookmarks bar
		60,  // Chrome/Edge without bookmarks
		90,  // Firefox with bookmarks
		70,  // Firefox without bookmarks
		100, // Safari
		120, // Browser with multiple toolbars
		40,  // Minimal browser UI
	];

	$maxScanHeight = min(150, $height);

	// Look for horizontal lines that might indicate chrome boundaries
	for ($testY = 30; $testY < $maxScanHeight; $testY += 5) {
		$isHorizontalLine = true;
		$baseColor = imagecolorat($image, $width / 2, $testY);

		// Check if this row has consistent color (horizontal line)
		for ($x = $width * 0.1; $x < $width * 0.9; $x += 10) {
			$rgb = imagecolorat($image, $x, $testY);
			if (abs($rgb - $baseColor) > 2000) { // Significant color difference
				$isHorizontalLine = false;
				break;
			}
		}

		if ($isHorizontalLine) {
			// Check if this matches a common browser height
			foreach ($commonHeights as $height) {
				if (abs($testY - $height) < 10) {
					return $testY;
				}
			}
		}
	}

	// Default fallback - assume 80px chrome height
	return 80;
}
?>