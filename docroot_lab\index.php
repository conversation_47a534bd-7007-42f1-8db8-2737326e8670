<?php
global $db_lab, $sqlCompleto, $language;
$language='enus';
$language='ptbr';
$language='esmx';
$language='frca';
$lang=$_REQUEST['lang'];
if (empty($lang)) $lang=$_COOKIE['lang'];
if(!empty($lang)){
	$language=$lang;
	setcookie('lang',$lang,time()+60*60*24*30*12*10,'/');
}
if (empty($language))$language='enus';
$_SESSION['language']=$language;
/*
$lang='en_US';
$lang='pt-BR';
// $lang='';
putenv("LANG=$lang");
putenv('LANGUAGE=');
putenv('LC_ALL='.$lang);
if (( $setTo = setlocale( LC_ALL, $lang )) === FALSE )
{
	echo "Unable to set a locale that the OS recognises.";
	return false;
}
$pathDomain=realpath(__DIR__.'../codebase_lab/function/locales');
$pathDomain=realpath(__DIR__.'/../').'/codebase_lab/function/locales';
$resp=bindtextdomain('main', $pathDomain);
// echo __DIR__."===<br/>";
// echo $pathDomain."===<br/>";
// echo $resp."===<br/>";
bind_textdomain_codeset('main', 'UTF-8');
textdomain('main');
*/
include_once('codebase_lab/function/func_lab.inc');
$mapN=$_REQUEST['map'];
if (empty($mapN)) $mapN=468;
$map=&FACTORY_lab::MAP();
if (!$map->loadByPk($mapN)){
	$map->map1_cod=$mapN;
	$map->insert();
}
$imgPath=getImgPath();
$msg=null;
$esc=900/1190;
$fator=20/1100;
$arAlli=getArrayAlli();
$arAlliMap=getArrayAlli($mapN);
$arPlay=getArrayPlay();
$arPlayMap=getArrayPlay($mapN);
$roleLeader=&FACTORY::ROLE();
$roleLeader->getByCodTxt('pcleader');
$leader=$acao->pessoa->checkRole($roleLeader->role2_cod);
$myUsername=$acao->pessoa->pess2_username;
$myPlayer=&FACTORY_lab::PLAYER();
$realUserCod=null;
if ($myPlayer->getByName($myUsername)){
	$realUserCod=$myPlayer->play1_cod;
	// echo nl2br(print_r($myPlayer, true))."<br/>";
	$myAlli=$arAlli[$myPlayer->alli1_cod];
	// echo nl2br(print_r($myAlli, true))."<br/>";
}else{
	$myAlli=null;
	$myPlayer=null;
}
$viewas=$_REQUEST['viewas'];
if (!empty($viewas)){
	$userPlayer=$myPlayer;
	$userAlli=$myAlli;
	$myPlayer=&FACTORY_lab::PLAYER();
	if ($myPlayer->loadByPk($viewas)){
		$myAlli=$arAlli[$myPlayer->alli1_cod];
	}else{
		$myAlli=null;
		$myPlayer=null;
	}
}else{
	$viewas=null;
}
// echo nl2br(print_r($myPlayer, true))."<br/>";

$planList=&FACTORY_lab::PLANET_LIST();
$sql="select ".$planList->getAster()."
from tb1_plan
where plan1_map=?";
$planList->loadBySQL($sql, [['integer',$mapN]]);
$wrmhList=&FACTORY_lab::WORMHOLE_LIST();
$sql="select ".$wrmhList->getAster()."
from tb1_wrmh
where wrmh1_map=?";
$wrmhList->loadBySQL($sql, [['integer',$mapN]]);
$arPlan=[];
while($plan=$planList->getNextItem()){
	$o=$plan->toMapJson($imgPath);
	$arPlan[]=$o;
}

$arWh=[];
while($wrmh=$wrmhList->getNextItem()){
	// $arWh[]=['id'=>$wrmh->wrmh1_cod,'x'=>$wrmh->wrmh1_x,'y'=>$wrmh->wrmh1_y,'maps'=>$wrmh->wrmh1_maps];
	$arWh[]=$wrmh->toMapJson();
}
$refMap=null;
if (file_exists('images/refmap'.$mapN.'.jpg')) $refMap='images/refmap'.$mapN.'.jpg';
if (!$refMap && file_exists('images/refmap'.$mapN.'.png')) $refMap='images/refmap'.$mapN.'.png';
// echo nl2br(print_r($arPlan, true))."<br/>";

// die('aqui '.$imgPath);
?>
	<title>Planet Capture Map</title>
	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.8.0/dist/leaflet.css" integrity="sha512-hoalWLoI8r4UszCkZ5kL8vayOGVae1oxXe/2A4AO6J9+580uKHDO3JdHb7NzwwzK5xr/Fs0W40kiNHxM9vyTtQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.8.0/dist/leaflet.js" integrity="sha512-BB3hKbKWOc9Ez/TAwyWxNXeoV9c1v6FIeYiBieIWkpLjauysF18NzgR1MBNBXf8/KABdlkX68nAhlwcDFLGPCQ==" crossorigin=""></script>
	<script src="contextmenu/leaflet.contextmenu.js"></script>
	<link rel="stylesheet" href="contextmenu/leaflet.contextmenu.css"/>
	<!-- <script src="https://code.jquery.com/jquery-3.6.4.min.js" integrity="sha256-oP6HI9z1XaZNBrJURtCoUT5SUnxFr8s3BzRl+cbzUq8=" crossorigin="anonymous"></script> -->
	<link rel="stylesheet" href="pcmap.css?v=1"/>
<script>
var difServerTime=<?=time()*1000?>-new Date();
console.log('difServerTime='+difServerTime);
var mapN=<?=$mapN?>;
var refMap=<?=($refMap?"'".$refMap."'":'null')?>;
var langTxt={};
var lang='<?=$lang?>';
var viewas=<?=($viewas?'true':'false')?>;
<?
$arJsLangTxt=['distribution','mapped','allies','planetsonmap','mappedenemies','unmappedenemies','addshields','removeshields','unknownshields'];
foreach($arJsLangTxt as $k=>$txt){
	?>langTxt['<?=$txt?>']='<?=lang($txt)?>';<?
}
?>
</script>
	<script src="pcmap.js?v=9"></script>
<body style="background-color:white;">
<style>
body{
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	font-size:14px;
}
#mapNum{
	position:absolute;
	left:48%;
	bottom:10px;
	color:yellow;
	border:solid 0px yellow;
	z-index:1000;
	font-size:1.5em;
	font-weight:bold;
}
#playerViewAs{
	position:absolute;
	left:50%;
	transform: translateX(-50%);
	top:10px;
	color:yellow;
	border:solid 0px yellow;
	z-index:1000;
	font-size:1em;
	font-weight:normal;
}
#coords{
	position:absolute;
	right:20px;
	bottom:16px;
	color:white;
	border:solid 0px yellow;
	z-index:1000;
}
.link{
	color:navy;
	cursor:pointer;
}
#fTable{
	width:100%;
}
.tdf{
	border-right:solid 1px lightgray;
	vertical-align: top;
}
.leaflet-tooltip{
	font-size:1.4em;
	line-height: 1.4em;
}
.distPercItem{
	display:inline-block;
	padding:0 2px;
	vertical-align: top;
}
</style>
<?
cospeJSFuncoesCookies();
$agora=time();
$gameTime=$agora+5*60*60;
$mexTime=$agora-3*60*60;
$isrTime=$agora+5*60*60;
$tzGame=new DateTimeZone('Europe/Zurich');
$dtGame=date_create('now',$tzGame);
$gameTime=$dtGame->getTimestamp();
$tzBr=new DateTimeZone('America/Sao_Paulo');
$dtBr=date_create('now', $tzBr	);
$brTime=$dtBr->getTimestamp();
$tzMex=new DateTimeZone('America/Mexico_City');
$dtMex=date_create('now',$tzMex);
$tzCan=new DateTimeZone('America/Toronto');
$dtCan=date_create('now',$tzCan);
$tzIsr=new DateTimeZone('Asia/Jerusalem');
$dtIsr=date_create('now', $tzIsr);


$agora=$dtBr->getTimestamp();
// echo $dtGame->format('H:i:s')." game<br/>";
$offsetG=$tzGame->getOffset($dtGame);
// echo $offsetG." offset<br/>";
$offsetBr=$tzBr->getOffset($dtBr);
// echo $offsetBr." offset BR<br/>";
$offsetMex=$tzMex->getOffset($dtMex);
// echo $offsetMex." offset MX<br/>";
$offsetCan=$tzCan->getOffset($dtCan);
// echo $offsetCan." offset MX<br/>";
$offsetIsr=$tzIsr->getOffset($dtIsr);
// echo $offsetCan." offset MX<br/>";
$plusViewAs=(empty($viewas)?'':'&viewas='.$viewas);
// $myAlli='INSOMNIACS';
?>
<script>
var gameOffset=<?=$offsetG?>;
var brOffset=<?=$offsetBr?>;
var mxOffset=<?=$offsetMex?>;
var caOffset=<?=$offsetCan?>;
var ilOffset=<?=$offsetIsr?>;
var myAlli='<?=($myAlli?$myAlli->alli1_nam:'UNKNOWN')?>';
</script>
<?
// echo nl2br(print_r($_SESSION, true))."<br/>";
?>
<table cellpadding="0" cellspacing="0" border="0" id="fTable">
	<tr>
		<td style="position:relative;vertical-align:top;width:900px;">
			<div id="map-wrapper" style="width: 900px; height: 900px;position:relative;">
				<div id="pcmap" style="width: 100%; height: 100%;"></div>
				<div id="playerViewAs"><?=($viewas?lang('viewas').' <b>'.$myPlayer->play1_nam.'</b>':'')?></div>
				<div id="mapNum"><?=$mapN?></div>
				<div id="coords"><span style="color:gray;">Move mouse over the map</span></div>
			</div>
		</td>
		<td style="vertical-align: top;padding:2px 10px;position:relative;">
			<div style="position:absolute;right:2px;top:2px;"><?=($language=='enus'?'<span  style="font-weight:bold;">EN</span>':'<a href="index.php?map='.$mapN.'&lang=enus'.$plusViewAs.'">EN</a>')?> | <?=($language=='esmx'?'<span  style="font-weight:bold;">ES</span>':'<a href="index.php?map='.$mapN.'&lang=esmx'.$plusViewAs.'">ES</a>')?> | <?=($language=='frca'?'<span  style="font-weight:bold;">FR</span>':'<a href="index.php?map='.$mapN.'&lang=frca'.$plusViewAs.'">FR</a>')?> | <?=($language=='ptbr'?'<span  style="font-weight:bold;">PT</span>':'<a href="index.php?map='.$mapN.'&lang=ptbr'.$plusViewAs.'">PT</a>')?></div>
			<span style="cursor:pointer;" onclick="toggle('divFilters')"><b><?=lang('filterutility')?></b> <i id="icoFilters" class="fa fa-caret-up"></i></span>
			<div>
				<div id="divFilters">
					<table id="tabFilters">
						<tr>
							<td class="tdf" style="padding-right:4px;">
								<div id="filters">
									<strong><?=lang('filter')?></strong><br/>
									<table cellpadding="2" border="0">
										<tr>
											<td><?=lang('alliance')?></td>
											<td colspan="3">
												<select id="fAlli" style="width:150px;appearance:auto;" onchange="addFilter('fAlli',$(this).val(),$(this).find('option:selected').text());$(this).val('')">
													<option value=""><?=lang('selalliance')?></option>
													<option value="theirs"><?=lang('enemies')?></option>
													<option value="ours"><?=lang('ours')?></option>
<?
foreach($arAlliMap as $alliCod=>$alli){
	?><option value="<?=$alliCod?>"><?=$alli->alli1_nam?></option><?
}
?>
												</select>
											</td>
										</tr>
										<tr>
											<td colspan="4"><div id="fbox-fAlli"></div></td>
										</tr>
										<tr>
											<td><?=lang('player')?></td>
											<td colspan="3">
											<select name="fPlay" id="fPlay" style="width:150px;appearance:auto;" onchange="addFilter('fPlay',$(this).val(),$(this).find('option:selected').text());$(this).val('')">
													<option value=""><?=lang('selplayer')?></option>
<?
foreach($arPlayMap as $playCod=>$play){
	if (empty(trim($play->play1_nam)))continue;
	?><option value="<?=$playCod?>"><?=$play->play1_nam?></option><?
}
?>
												</select>
												<!-- <input style="width:150px;padding:0;height:22px;" type="text" name="fUser" id="fUser" value="" onkeyup="doFilters()" onchange="doFilters()"/> -->
											</td>
										</tr>
										<tr>
											<td colspan="4"><div id="fbox-fPlay"></div></td>
										</tr>
										<tr>
											<td><?=lang('guns')?> &lt;</td>
											<td><input style="width:50px;padding:0;height:22px;" type="number" name="fGuns" id="fGuns" value="" onkeyup="doFilters()" onchange="doFilters()"/></td>
											<td><?=lang('level')?> =</td>
											<td><select style="width:20px;text-align:center;" name="fGunLevel" id="fGunLevel" onchange="doFilters()"><option value="">--</option><option value="1" style="color:darkgreen">1</option><option value="2" style="color:darkred">2</option><option value="3" style="color:darkblue">3</option></select></td>
										</tr>
									</table>
									<div id="filterSummary"></div>
								</div>
							</td>
							<td class="tdf" style="padding:2px 4px;">
								<div id="utilities">
									<strong><?=lang('utility')?></strong><br/>
									<table cellpadding="2" border="0">
										<tr>
											<td><?=lang('map')?></td>
											<td style="white-space:nowrap;"><input style="width:60px;padding:0;height:22px;" type="text" name="map" id="fmap" onkeyup="enterGoToMap(event)" value="" /> <button style="height:22px;line-height:17px;font-size:12px;vertical-align:bottom;" onclick="goToMap($('#fmap').val())"><?=lang('go')?></button> <button type="button" style="height:22px;line-height:17px;font-size:12px;vertical-align:bottom;" onclick="showWhFromMap($('#fmap').val())"><?=lang('wh')?></button></td>
										</tr>
										<tr>
											<td><?=lang('locate')?></td>
											<td style="white-space:nowrap;"><input style="width:60px;padding:0;height:22px;" type="text" name="fX" id="fX" value="" onkeyup="showLocation()"/>:<input style="width:60px;padding:0;height:22px;" type="text" name="fY" id="fY" value="" onkeyup="showLocation()"/> <i class="fa fa-close" onclick="clearLocation()"></i></td>
										</tr>
										<tr>
											<td colspan="2">
												<div id="divMapReference" style="width:200px;">
													<strong><?=lang('refmapopac')?></strong><br/>
													<div id="refmap-slider" class="range"><input type="range" class="form-range" id="refMapOpacity" value="50" onchange="mapRefOpacity(this)" /></div>
												</div>
											</td>
										</tr>
										<tr>
											<td colspan="2">
												<span class="link" id="spanToggleGrid" onclick="toggleGrid()"><button style="padding:0 3px 3px 3px;" title="<?=lang('togglegrid')?>"><img src="images/togglegrid.png"/></button></span>
												&nbsp;&nbsp;<span class="link" id="spanNavToShl" onclick="toggleNavShield()"><button style="padding:0 3px 3px 3px;" title="<?=lang('toggleshldet')?>"><img src="images/detshld.png" style="width:17px;height:17px;"/></button></span>
												&nbsp;&nbsp;<span class="link" id="spanImpUser" onclick="toggleImpUser()"><button style="padding:0 3px 3px 3px;" title="<?=lang('viewas')?>"><img src="images/impuser.png" style="width:17px;height:17px;"/></button></span>
											</td>
										</tr>
									</table>
								</div>
							</td>
							<td class="tdf" style="padding:2px 4px;">
								<!-- <b><?=lang('timezone')?>:</b> <?=lang('game')?> <?=showSoHora($gameTime)." | BR ".showSoHora($agora)." | MX ".showSoHora($mexTime)."<br/>"?> -->
								<b>Time zones</b><br/>
								<b>IL</b> <span id="iltime"></span> | <b><?=lang('game')?></b> <span id="gametime"></span> | <b>BR</b> <span id="brtime"></span> | <b>CA</b> <span id="catime"></span> | <b>MX</b> <span id="mxtime"></span><br/>
								<b><?=lang('legend')?></b>
								<table style="background-color:black;color:white;">
									<tr>
										<td><?=lang('planets')?></td>
										<td><div style="display:inline-block;width:14px;height:14px;border-radius:10px;border:solid 2px lightgray;"></div> <?=lang('wormhole')?></td>
										<td><div style="display:inline-block;width:14px;height:14px;border-radius:10px;border:solid 2px lightgreen;"></div> <?=lang('yours')?></td>
										<td><div style="display:inline-block;width:14px;height:14px;border-radius:10px;border:solid 2px blue;"></div> <?=lang('ours')?></td>
										<td><div style="display:inline-block;width:14px;height:14px;border-radius:10px;border:solid 2px red;"></div> <?=lang('enemies')?></td>
									</tr>
									<tr>
										<td><?=lang('guns')?>:</td>
										<td><div style="display:inline-block;width:14px;height:14px;border-radius:10px;border:dashed 1px white;"></div> <?=lang('noguninfo')?></td>
										<td><div style="display:inline-block;width:14px;height:14px;border-radius:10px;border:solid 1px white;"></div> Level 1</td>
										<td><div style="display:inline-block;width:14px;height:14px;border-radius:10px;border:solid 2px white;"></div> Level 2</td>
										<td><div style="display:inline-block;width:14px;height:14px;border-radius:10px;border:solid 3px white;"></div> Level 3</td>
									</tr>
									<tr>
										<td colspan="5"><?=lang('radius')?></td>
									</tr>
								</table>
								<?if ($leader){?><a href="adm/" target="_blank"><?=lang('manage')?></a><?}?>
							</td>
						</tr>
					</table>
				</div>
			</div>

<?
if ($msg){
?>
<div class="alert alert-danger"><?=$msg?></div>
<?
}
?>
			<b><?=lang('planets')?></b> (<?=lang('planetinstr')?>)
			<div style="margin:0;border-top:solid 1px black;border-bottom:solid 1px lightgray;">
				<span class="link" id="linkUpload" onclick="$('#divFormUpload').show();$('#linkUpload').hide();"><?=lang('showform')?></span>
				<div id="divFormUpload"style="display:none;">
					<span class="link" id="linkCloseUpload" onclick="$('#divFormUpload').hide();$('#linkUpload').show();">Hide upload form</span>
					<form id="formUpload" action="" method="POST" enctype="multipart/form-data" onsubmit="return preUpload();">
						<input type="hidden" id="hidUploadAction" name="m" value="upload"/>
						<div style="border:solid 1px black;padding:2px;">
							<table>
								<tr>
									<td><?=lang('player')?></td>
									<td colspan="3">
										<input type="text" name="user" id="upUser" style="width: 238px;" value=""/>
										&nbsp; <span class="link" id="linkNewAlli" style="display:none_;" onclick="newAlliance()"><?=lang('newalliance')?></span>
									</td>
									<td id="obsUser"></td>
								</tr>
								<tr>
									<td><?=lang('alliance')?></td>
									<td colspan="3">
										<input type="text" name="team" id="upTeam" style="width: 238px;" value=""/>
									</td>
									<td id="obsTeam"></td>
								</tr>
								<tr>
									<td><?=lang('position')?></td>
									<td><input type="text" name="map" name="id" size="5" value="<?=$mapN?>"/> : </td>
									<td><input type="text" name="x" id="upX" size="5" value=""/> : </td>
									<td><input type="text" name="y" id="upY" size="5" value=""/></td>
									<td>(map:x:y)</td>
								</tr>
								<tr>
									<td><?=lang('guns')?></td>
									<td><input type="text" name="guns" id="guns" size="5" value=""/></td>
									<td>Level</td>
									<td><input type="text" name="level" id="level" size="5" value=""/></td>
									<td></td>
								</tr>
								<tr>
									<td><?=lang('security')?></td>
									<td><label><input type="checkbox" name="detection" id="detection" value="true"/> Detection</label></td>
									<td><label><input type="checkbox" name="jammer" id="jammer" value="true"/> Jammer</label></td>
									<td><label><input type="checkbox" name="sensor" id="sensor" value="true"/> Sensors</label></td>
									<td></td>
								</tr>
								<tr>
									<td><?=lang('repair')?></td>
									<td><label><input type="radio" name="repair" id="repair1" value="true"/> Level 1</label></td>
									<td><label><input type="radio" name="repair" id="repair2" value="true"/> Level 2</label></td>
									<td><label><input type="radio" name="repair" id="repair3" value="true"/> Level 3</label></td>
									<td><label><input type="radio" name="repair" id="repair" value=""/> none</label></td>
								</tr>
								<tr>
									<td colspan="4"><input type="file" name="plfile" id="plfile" value="" onchange="parseFname()"/></td>
									<td align="right"><button id="butSubmitUpload" type="submit"><?=lang('sendimage')?></button></td>
								</tr>
								<tr>
									<td colspan="5" style="font-size:10px;"><b><?=lang('hints')?>:</b> <?=lang('hinttxt')?>
								</td>
								</tr>
							</table>
						</div>
						</form>
				</div>
			</div>

			<div style="margin:10px 0;border-top:solid 1px black;border-bottom:solid 1px lightgray;">
				<span class="link" id="linkUploadMap" onclick="$('#divFormUploadMap').show();$('#linkUploadMap').hide();"><b>Upload Map Screenshot</b></span>
				<div id="divFormUploadMap" style="display:none;">
					<span class="link" id="linkCloseUploadMap" onclick="$('#divFormUploadMap').hide();$('#linkUploadMap').show();">Hide map upload form</span>
					<form id="formUploadMap" action="" method="POST" enctype="multipart/form-data" onsubmit="return preUploadMap();">
						<input type="hidden" name="m" value="uploadMap"/>
						<div style="border:solid 1px black;padding:2px;">
							<table>
								<tr>
									<td><b>Map Screenshot:</b></td>
									<td colspan="3">
										<input type="file" name="mapfile" id="mapfile" accept="image/*" required/>
									</td>
									<td align="right"><button id="butSubmitUploadMap" type="submit">Upload & Process Map</button></td>
								</tr>
								<tr>
									<td colspan="5" style="font-size:10px;padding:5px;">
										<b>Instructions:</b> Upload a screenshot of the Planet Capture game map. The system will automatically detect planets from the mini-map in the top-right corner and add any new planets to the database.
									</td>
								</tr>
							</table>
						</div>
					</form>
				</div>
			</div>
			<input type="hidden" id="txt-addbyclick" name="txt-addbyclick" value="<?=lang('addbyclick')?>"/>
			<input type="hidden" id="txt-updateplanet" name="txt-updateplanet" value="<?=lang('updateplanet')?>"/>
			<input type="hidden" id="txt-addwhbyclick" name="txt-addwhbyclick" value="<?=lang('addwhbyclick')?>"/>
			<input type="hidden" id="txt-updatewh" name="txt-updatewh" value="<?=lang('updatewh')?>"/>
			<div style="margin:10px 0;display:none;" id="infoEditPl">
				<strong id="titEditPl">Update planet info</strong> <span class="link" onclick="$('#infoEditPl').hide();">hide</span>
				<form id="formUpdatePl" action="" method="POST" onsubmit="return doUpdatePl()">
					<input type="hidden" id="hidEditAction" name="m" value="updatePl"/>
					<input type="hidden" id="iep-id" name="id" value=""/>
					<div style="border:solid 1px black;padding:2px;">
						<table>
							<tr>
								<td><?=lang('player')?></td>
								<td colspan="3"><input name="iep-user" id="iep-user" value=""></td>
								<td colspan="2"><span class="link" id="linkChangeAlli" style="display:none_;" onclick="editPlay()"><?=lang('changealliance')?></span></td>
							</tr>
							<tr>
								<td><?=lang('alliance')?></td>
								<td colspan="3"><input name="iep-team" id="iep-team" value="" disabled></td>
								<td colspan="2"><span class="link" id="linkNewAlli" style="display:none_;" onclick="newAlliance()"><?=lang('newalliance')?></span></td>
							</tr>
							<tr>
								<td><?=lang('position')?></td>
								<td><input name="iep-map" id="iep-map" size="5" value=""> : </td>
								<td><input name="iep-x" id="iep-x" size="5" value=""> : </td>
								<td><input name="iep-y" id="iep-y" size="5" value=""></td>
								<td>(map:x:y)</td>
							</tr>
							<tr>
								<td><?=lang('guns')?></td>
								<td><input type="text" name="guns" id="iep-guns" size="5" value=""/></td>
								<td>
									<select name="level" id="iep-level" style="width:70px;height:auto;line-height:23px;padding:1px 2px;">
										<option value="1" style="color:green;">1 green</option>
										<option value="2" style="color:red;">2 red</option>
										<option value="3" style="color:blue;" selected>3 blue</option>
									</select>
								</td>
								<td>
									<select name="shields" id="iep-shield" style="width:70px;height:auto;line-height:23px;padding:1px 2px;">
										<option value="null" style="color:gray;"><?=lang('shieldsUnknown')?></option>
										<option value="true" style="color:orange;"><?=lang('shieldsOn')?></option>
										<option value="false" style="color:darkgreen;" selected><?=lang('shieldsOff')?></option>
									</select>
								</td>
								<td><span id="shieldedTimeTitle"><?=lang('shieldedtime')?></span> <span id="iep-shieldedtime"></span></td>
							</tr>
							<!-- <tr>
								<td><?=lang('shields')?></td>
								<td>
									<button id="iep-addshieldbutton" onclick="return addShields()" style="display:none;" type="button"><?=lang('addshields')?></button>
									<button id="iep-removeshieldbutton" onclick="return removeShields()" style="display:none;" type="button"><?=lang('removeshields')?></button>
								</td>
								<td><span id="shieldedTimeTitle"><?=lang('shieldedtime')?></span></td>
								<td id="iep-shieldedtime"></td>
								<td></td>
							</tr> -->
							<tr>
								<td><?=lang('security')?></td>
								<td><label><input type="checkbox" name="sensor" id="iep-sensor" value="true"/> Sensor</label></td>
								<td><label><input type="checkbox" name="jammer" id="iep-jammer" value="true"/> Jammer</label></td>
								<td><label><input type="checkbox" name="detection" id="iep-detection" value="true"/> Detection</label></td>
								<td></td>
							</tr>
							<tr>
								<td><?=lang('repair')?></td>
								<td><label><input type="radio" name="repair" id="iep-repair1" value="1"/> Level 1</label></td>
								<td><label><input type="radio" name="repair" id="iep-repair2" value="2"/> Level 2</label></td>
								<td><label><input type="radio" name="repair" id="iep-repair3" value="3"/> Level 3</label></td>
								<td><label><input type="radio" name="repair" id="iep-repair" value=""/> none</label></td>
								<td></td>
							</tr>
							<tr>
								<td colspan="2">
									<button id="butDeletePl" type="button" onclick="return delPlan()"><?=lang('delete')?></button>
									<button id="butCloseEditPl" type="button" onclick="closeEditPl()"><?=lang('close')?></button>
								</td>
								<td colspan="3" align="right"><button id="butUpdatePl" type="submit"><?=lang('update')?></button></td>
							</tr>
						</table>
					</div>
					</form>
				</div>
			</div>
			<div style="margin:10px 0;display:none;" id="infoEditWh">
				<strong id="titEditWh">Update wormhole info</strong> <span class="link" onclick="$('#infoEditWh').hide();"><?=lang('hide')?></span>
				<form id="formUpdateWh" action="" method="POST" onsubmit="return doUpdateWh()">
					<input type="hidden" id="hidEditActionWh" name="m" value="updateWh"/>
					<input type="hidden" id="iew-id" name="id" value=""/>
					<div style="border:solid 1px black;padding:2px;">
						<table>
							<tr>
								<td><?=lang('position')?></td>
								<td><input name="iew-map" id="iew-map" size="5" value=""> : </td>
								<td><input name="iew-x" id="iew-x" size="5" value=""> : </td>
								<td><input name="iew-y" id="iew-y" size="5" value=""></td>
								<td>(map:x:y)</td>
							</tr>
							<tr>
								<td><?=lang('goodformaps')?></td>
								<td colspan="4"><input type="text" name="goodmaps" id="iew-maps" size="45" value=""/></td>
							</tr>
							<tr>
								<td colspan="2"><button id="butDeleteWh" type="button" onclick="return delWh()"><?=lang('delete')?></button></td>
								<td colspan="3" align="right"><button id="butUpdateWh" type="submit"><?=lang('update')?></button></td>
							</tr>
						</table>
					</div>
					</form>
				</div>
			</div>
			<span style="cursor:pointer;" onclick="toggle('divStats')"><b><?=lang('stat')?></b> <i id="icoStats" class="fa fa-caret-up"></i></span>
			<div>
			<div id="divStats" style="border-top:solid 1px gray;"></div>
			</div>
			<span style="cursor:pointer;" onclick="toggle('divLista')"><b><?=lang('userlist')?></b> <i id="icoLista" class="fa fa-caret-up"></i></span>
			<div>
				<div id="divLista" style="margin:0;border-top:solid 1px black;border-bottom:solid 1px lightgray;font-size:0.86em;max-height:300px;overflow-y:auto;">
<style>
#tabLista td{
	padding:2px 4px;
	border-bottom:solid 1px lightgray;
}
</style>
				<table id="tabLista">
<?
$playPlans=getArrayPlayPlanets($mapN);
arsort($playPlans);
foreach($playPlans as $playCod=>$qtd){
	$play=$arPlay[$playCod];
	$allTxt='-';
	if (!empty($play->alli1_cod))$allTxt=$arAlli[$play->alli1_cod]->alli1_nam;
	// if ($playCod=3) echo nl2br(print_r($play, true))."<br/>";
	if ($allTxt==$myAlli->alli1_nam)$allTxt='<b>'.$allTxt.'</b>';
?>
						<tr>
							<td><?=$play->play1_nam?></td>
							<td><?=$allTxt?></td>
							<td><?=$qtd?></td>
						</tr>
<?
}

?>
					</table>
				</div>
			</div>
			<span style="cursor:pointer;" onclick="toggle('divHelp')"><b><?=lang('helpinstr')?></b> <i id="icoHelp" class="fa fa-caret-up"></i></span>
			<div id="divHelp" style="margin:0;border-top:solid 1px black;border-bottom:solid 1px lightgray;font-size:0.86em;">
				<table id="tabHelp">
					<tr>
						<td style="vertical-align:top;font-weight:bold;"><?=lang('newplanetclick')?></td>
						<td><?=lang('newplanetclicktxt')?></td>
					</tr>
					<tr>
						<td style="vertical-align:top;font-weight:bold;"><?=lang('editplanetclick')?></td>
						<td><?=lang('editplanetclicktxt')?></td>
					</tr>
					<tr>
						<td style="vertical-align:top;font-weight:bold;"><?=lang('newplanet')?></td>
						<td><?=lang('newplanettxt')?></td>
					</tr>
					<tr>
						<td style="vertical-align:top;font-weight:bold;"><?=lang('editplanet')?></td>
						<td><?=lang('editplanettxt')?></td>
					</tr>
					<tr>
						<td style="vertical-align:top;font-weight:bold;"><?=lang('filters')?></td>
						<td><?=lang('filterstxt')?></td>
					</tr>
					<tr>
						<td style="vertical-align:top;font-weight:bold;"><?=lang('utilities')?></td>
						<td><?=lang('utilitiestxt')?></td>
					</tr>
				</table>
			</div>
			<div id="portaFormEditPlayer" style="display:none;">
				<div id="formEditPlay" class="content" style="min-height:50px;">
					<div class="row">
						<div class="col-sm-12">
							<div class="form-group">
								<label><?=lang('player')?></label>
								<input class="form-control" type="text" name="ep-play1_nam" id="ep-play1_nam" value="" placeholder="Player name"/>
							</div>
							<div class="form-group">
								<label><?=lang('alliance')?></label>
								<select class="form-control" name="ep-alli1_cod" id="ep-alli1_cod">
									<option value="">Select</option>
									<option value="0">No alliance</option>
<?
	foreach($arAlli as $alliCod=>$alli){
?>
									<option value="<?=$alliCod?>"><?=$alli->alli1_nam?></option>
<?
	}
?>
								</select>
							</div>
						</div>
					</div>
				</div>
			</div>
<?
if ($acao->pessoa->isRoleAdm){
?>
<script>
function delPlan(){
	if (confirm('Are you sure?')){
		var id=$('#iep-id').val();
		if (id=='') return false;
		$.ajax({
			url:'ajax_pc.php',
			type: 'POST',
			data: {m:'delPlan',cod:id},
			dataType:'script',
			success: function(data) {
			}, complete: function(){
				if (selected) {
					selected.removeFrom(map);
					selected=null;
				}
			}
		});
		return false;
	}
}
function delWh(){
	if (confirm('Are you sure?')){
		var id=$('#iew-id').val();
		if (id=='') return false;
		$.ajax({
			url:'ajax_pc.php',
			type: 'POST',
			data: {m:'delWh',cod:id},
			dataType:'script',
			success: function(data) {
			}, complete: function(){
			}
		});
		return false;
	}
}
</script>
<div class="box box-default collapsed-box">
	<div class="box-header with-border">
		<h3 class="box-title">Debug</h3>
		<div class="box-tools pull-right">
			<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
		</div>
		<!-- /.box-tools -->
	</div>
	<!-- /.box-header -->
	<div id="debugbox" class="box-body">
		<?=@$output?>
	</div>
	<!-- /.box-body -->
</div>
<?
}
$arPlayers=[];
foreach($arPlay as $playCod=>$play){
	$arPlayers[$playCod]=['name'=>$play->play1_nam,'alli'=>$play->alli1_cod];
}
?>
		</td>
	</tr>

</table>
<script>
var un='<?=$myUsername?>';
var ruc=<?=$realUserCod?>;
var uc=<?=$myPlayer->play1_cod?>;
var mapN=<?=$mapN?>;
var esc=<?=$esc?>;
var fator=<?=$fator?>;
var arWhs=<?=json_encode($arWh)?>;
var planets=<?=json_encode($arPlan)?>;
var players=<?=json_encode($arPlayers)?>;
</script>
<?
?>