#fTable td{
	padding:2px;
}
.gridIco{
	width:48px;
	height: 28px;
	background-color: transparent;
	color:lightgray;
	font-size: 11px;
	border:solid 0px lightgray;
	line-height: 14px;
	font-weight:bold;
}
.repIco1{
	width:28px;
	height: 28px;
	background-color: transparent;
	color:lightgray;
	font-size: 11px;
	border:solid 1px lightgray;
	line-height: 14px;
	text-align: center;
}
.repIco2{
	width:28px;
	height: 28px;
	background-color: transparent;
	color:rgb(0, 151, 201);
	font-size: 11px;
	border:solid 1px rgb(0, 151, 201);
	line-height: 14px;
	text-align: center;
}
.repIco3{
	width:28px;
	height: 28px;
	background-color: transparent;
	color:blue;
	font-size: 11px;
	border:solid 1px blue;
	line-height: 14px;
	text-align: center;
}
.leaflet-tooltip {
	position: absolute;
	padding: 3px;
	background-color: #fff;
	border: 1px solid #fff;
	border-radius: 3px;
	color: #222;
	white-space: nowrap;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	pointer-events: none;
	box-shadow: 0 1px 3px rgba(0,0,0,0.4);
	font-size:10px;
	line-height: 12px;
}
.floatfiltro {
	font-size: 11px;
	height: 19px;
	max-width: 100px;
	border-radius: 10px;
	background-color: blue;
	color: white;
	display: inline-flex;
	padding: 2px 6px;
	margin: 2px 4px;
	white-space: nowrap;
}
.fafiltro {
	font-size: 13px;
	cursor: pointer;
}
.txtfloatfiltro {
	overflow: hidden;
}
#distTable td, #totalsTable td{
	padding:0 3px;
	font-size:0.85em;
	line-height: 1.2em;
}
#tabGraf{
	margin:2px 0;
	width: 100%;
}
#tabGraf td{
	height:16px;
}
#tabHelp td{
	vertical-align: top;
	border-bottom:solid 1px lightgray;
}
.leaflet-popup-content{
	font-size:13px;
	margin: 7px 15px 7px 11px;
}

/* Incomplete planet styling */
.incomplete-planet {
	background-color: #808080 !important;
	border: 2px solid #606060 !important;
}