<?php
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;
$global_before = getmicrotime();
$action = (isset($_REQUEST["action"])?@$_REQUEST["action"]:null);
$construseek = (isset($_REQUEST["construseek"]) && @$_REQUEST["construseek"]=='true');
$TP = (isset($_REQUEST["TP"]) && @$_REQUEST["TP"]);
@include('config/func_especificas.inc');
$statusErroEnvio=array("REP"=>"Endereço de e-mail repetido", "PRP"=>"Pessoa repetida", "ENV"=>"Erro no ato do envio", "MBF"=>"Caixa postal cheia", "INV"=>"Endereço inválido", "NX"=>"Endereço inexistente", "SPA"=>"Recusado por Anti-spam", "LOG"=>"Não foi possível armazenar no log de envio");
$translate_from='áàâãéêíóôõúç,.-()!/\\´`~^¨';
$translate_to  ='aaaaeeiooouc             ';
$GLOBALS['php_errormsg']=null;
function getArrayBeans(){
	global $_arrayBeans;
	if (@$_arrayBeans) return $_arrayBeans;
	/********************************
	formato do array:
	key é o codtxt do bean
	value é um array
		func=>NOME_DA_CLASSE_DO_BEAN
		inc=>path/para/o/bean.inc
	********************************/
	$_arrayBeans = array(
	'conteudo'=>array('func'=>'BEANCONTEUDO', 'inc'=>"codebase/function/beans/conteudo.inc")
	//, 'listadirepath'=>array('func'=>'LISTADIREPATH', 'inc'=>"codebase/function/beans/listadirepath.inc")
	//, 'anuncio'=>array('func'=>'BEANANUNCIO', 'inc'=>"codebase/function/beans/anuncio.inc")
	, 'arquivos'=>array('func'=>'BEANARQUIVOS', 'inc'=>"codebase/function/beans/arquivos.inc")
	//, 'pesquisa'=>array('func'=>'BEANPESQUISA', 'inc'=>"codebase/function/beans/pesquisa.inc")
	, 'clipping'=>array('func'=>'BEANCLIPPING', 'inc'=>"codebase/function/beans/clipping.inc")
	//, 'forum'=>array('func'=>'BEANFORUM', 'inc'=>"codebase/function/beans/forum.inc")
	//, 'associacao'=>array('func'=>'BEANASSOCIACAO', 'inc'=>"codebase/function/beans/associacao.inc")
	, 'calendario'=>array('func'=>'BEANCALENDARIO', 'inc'=>"codebase/function/beans/calendario.inc")
	//, 'sac'=>array('func'=>'BEANSAC', 'inc'=>"codebase/function/beans/sac.inc")
	, 'cadastro'=>array('func'=>'BEANCADASTRO', 'inc'=>"codebase/function/beans/cadastro.inc")
	//, 'menu'=>array('func'=>'BEANMENU', 'inc'=>"codebase/function/beans/menu.inc")
	//, 'projeto'=>array('func'=>'BEANPROJETO', 'inc'=>"codebase/function/beans/projeto.inc")
	//, 'revenda'=>array('func'=>'BEANREVENDA', 'inc'=>"codebase/function/beans/revenda.inc")
	//, 'link'=>array('func'=>'BEANLINK', 'inc'=>"codebase/function/beans/link.inc")
	, 'contato'=>array('func'=>'BEANCONTATO', 'inc'=>"codebase/function/beans/contato.inc")
	, 'resultmailcenter'=>array('func'=>'BEANRESULTMAILCENTER', 'inc'=>"codebase/function/beans/resultmailcenter.inc")
	, 'mailer'=>array('func'=>'BEANMAILER', 'inc'=>"codebase/function/beans/mailer.inc")
	, 'seletor'=>array('func'=>'BEANSELETOR', 'inc'=>"codebase/function/beans/seletor.inc")
	//, 'statmkt'=>array('func'=>'BEANSTATMKT', 'inc'=>"codebase/function/beans/statmkt.inc")
	, 'statmktv2'=>array('func'=>'BEANSTATMKT2', 'inc'=>"codebase/function/beans/statmktv2.inc")
	//, 'produto'=>array('func'=>'BEANPRODUTO', 'inc'=>"codebase/function/beans/produto.inc")
	//, 'produtoadm'=>array('func'=>'BEANPRODUTOADM', 'inc'=>"codebase/function/beans/produtoadm.inc")
	, 'seriehistorica'=>array('func'=>'BEANSERIEHISTORICA', 'inc'=>"codebase/function/beans/seriehistorica.inc")
	, 'seriehistorica_adm'=>array('func'=>'BEANSERIEHISTORICA_ADM', 'inc'=>"codebase/function/beans/seriehistorica_adm.inc")
	, 'listacadastros'=>array('func'=>'BEANLISTACADASTROS', 'inc'=>"codebase/function/beans/listacadastros.inc")
	, 'listadirepath2'=>array('func'=>'BEANLISTADIREPATH2', 'inc'=>"codebase/function/beans/listadirepath2.inc")
	, 'busca'=>array('func'=>'BEANBUSCA', 'inc'=>"codebase/function/beans/busca.inc")
	//, 'forum2'=>array('func'=>'BEANFORUM2', 'inc'=>"codebase/function/beans/forum2.inc")
	//, 'oferta'=>array('func'=>'BEANOFERTA', 'inc'=>"codebase/function/beans/oferta.inc")
	//, 'publicacao'=>array('func'=>'BEANPUBLICACAO', 'inc'=>"codebase/function/beans/publicacao.inc")
	, 'noticia'=>array('func'=>'BEANNOTICIA', 'inc'=>"codebase/function/beans/noticia.inc")
	//, 'autores'=>array('func'=>'AUTORES', 'inc'=>"codebase/function/beans/autores.inc")
	//, 'enviapagina'=>array('func'=>'BEANENVIAPAGINA', 'inc'=>"codebase/function/beans/enviapagina.inc")
	//, 'pathcomtemplate'=>array('func'=>'BEANPATHCOMTEMPLATE', 'inc'=>"codebase/function/beans/pathcomtemplate.inc")
	, 'direcomtemplate'=>array('func'=>'BEANDIRECOMTEMPLATE', 'inc'=>"codebase/function/beans/direcomtemplate.inc")
	, 'listaarquivos'=>array('func'=>'BEANLISTAARQUIVOS', 'inc'=>"codebase/function/beans/listaarquivos.inc")
	, 'maileroptinoptout'=>array('func'=>'BEANMAILEROPTINOPTOUT', 'inc'=>"codebase/function/beans/maileroptinoptout.inc")
	//, 'grupodetrabalho'=>array('func'=>'BEANGRUPODETRABALHO', 'inc'=>"codebase/function/beans/grupodetrabalho.inc")
	, 'consultaseries'=>array('func'=>'BEANCONSULTASERIES', 'inc'=>"codebase/function/beans/consultaseries.inc")
	//, 'eventoadm'=>array('func'=>'BEANEVENTOADM', 'inc'=>"codebase/function/beans/eventoadm.inc")
	//, 'evento'=>array('func'=>'BEANEVENTO', 'inc'=>"codebase/function/beans/evento.inc")
	//, 'avaliaadm'=>array('func'=>'BEANAVALIAADM', 'inc'=>"codebase/function/beans/avaliaadm.inc")
	//, 'clienteproduto'=>array('func'=>'BEANCLIENTEPRODUTO', 'inc'=>"codebase/function/beans/clienteproduto.inc")
	//, 'loja'=>array('func'=>'BEANLOJA', 'inc'=>"codebase/function/beans/loja.inc")
	//, 'monitorpessoa'=>array('func'=>'BEANMONITORPESSOA', 'inc'=>"codebase/function/beans/monitorpessoa.inc")
	//, 'gesfin'=>array('func'=>'BEANGESFIN', 'inc'=>"codebase/function/beans/gesfin.inc")
	, 'acordeom'=>array('func'=>'BEANACORDEOM', 'inc'=>"codebase/function/beans/acordeom.inc")
	);
	return $_arrayBeans;
}

function isWindows(){
	if (strpos(strtolower(PHP_OS), 'win')===false) return false; else return true;
}
function emCommandLine(){
	$sapi = php_sapi_name();
	switch(strtolower($sapi)){
		case 'cgi':
		case 'cgi-fcgi':
			if (!$_SERVER["SERVER_SOFTWARE"]) return true;
			if ($_SERVER["SERVER_SOFTWARE"]=='') return true;
		case 'apache':
			return false;
		case 'cli':
			return true;
	}
}
function getAplicacoesBean($qual){
	$bean=&FACTORY::BEAN();
	$arBears=array();
	if ($bean->getByIdentificador_interno_do_bean($qual)){
		$list=$bean->getBEAN_DA_AREA_LIST();
		while ($item=$list->getNextItem()){
			$arBears[$item->bear3_cod]=$item;
		}
	}
	if (sizeof($arBears)>0) return $arBears; else return null;
}

/* Essa função retorna o valor de parâmetro especificado na linha de comando, ou true se o parâmetro foi especificado
   sem valor, ou false se não especificado... */
function getCmdLine($which, $default=null) {
	if (php_sapi_name()!=="cgi") return null;
	global $argv, $argc; $total = "";
	if (count($argv) < 2) return null;
	for ($i=1; $i < count($argv); $i++) {
		$total[] = $argv[$i];
	}
	$total = implode($total, " ");
	$bynaco = explode("--",$total);
	foreach ($bynaco as $naco) {
		if ($naco) {
			if (strtoupper(substr($naco, 0, strlen($which) )) == strtoupper($which)) {
				$present = true;
				$a = explode("=", $naco);
				if (count($a) > 1) {
					return $a[1];
				} else {
					return true;
				}
			}
		}
	}
	return false;
}

function getJSnovaJanela($pag, $width=770, $height=300, $scroll="yes", $resize="yes", $nomejanela="janela", $opts="dependent=no,directories=no,location=no,toolbar=no,statusbar=no,menubar=no") {
	return "$nomejanela = window.open('$pag','$nomejanela','$opts,width=$width,height=$height,scrollbars=$scroll,resizable=$resize'); $nomejanela.focus();";
}

function cospeJSnovaJanela($pag, $width=600, $height=400, $scroll="yes", $resize="yes", $nomejanela="janela", $opts="dependent=no,directories=no,location=no,toolbar=no,status=yes,menubar=no,left=null,top=null") {
	echo getJSnovaJanela($pag, $width, $height, $scroll, $resize, $nomejanela, $opts);
}

/*
// return para usar em strings por ai...
function cospeJSnovaJanela($pag, $width=600, $height=400, $scroll="yes", $resize="yes", $nomejanela="janela", $opts="dependent=no,directories=no,location=no,toolbar=no,status=yes,menubar=no") {
	return $nomejanela."=window.open('".$pag."','".$nomejanela."','".$opts.",width=".$width.",height=".$height.",scrollbars=".$scroll.",resizable=".$resize."'); ".$nomejanela.".focus()";
}*/



function cospeJSFechaPai($reload = true) {
	global $ja_cuspiu_js_fecha_pai;
	if (!$ja_cuspiu_js_fecha_pai){
	?>
<script language="JavaScript1.2" type="text/javascript">
var pai = window.opener;
if (pai!=null){
	pai.focus();
<?
	if ($reload) {
?>
	pai.document.location.reload();
<?
	}
?>
}
window.close();
</script>
<?php
		$ja_cuspiu_js_fecha_pai=true;
	}
}

function cospeFechaFocaPai($refresh=false) {
	cospeJSFechaPai($refresh);
	return;
?><SCRIPT>
	pai = window.opener;
	if (pai) {
		pai.focus();
	}
<?
	if ($refresh) {
?>
		pai = window.opener;
		if (pai) {
			pai.location.reload();
		}
<?
	}
	?>
	window.close();
	</SCRIPT>
	<?php
}

function getJSFechaPai($reload = true) {
	if ($reload) {
		return "window.opener.document.location.reload(); window.opener.focus(); window.close();";
	} else {
		return "window.opener.focus(); window.close();";
	}
}

function getQS(){
	$qs="";
	$sep="";
	if (count($_GET)>0){
		foreach ($_GET as $key=>$value){
			if ($key!="acao3_cod0") {
				if (is_array($value)){
					$$key=$_REQUEST[$key];
					foreach($$key as $subkey=>$subvalue){
						$qs.=$sep.$key."[".$subkey."]"."=".urlencode($subvalue);
						$sep="&";
					}
				}else{
					$qs.=$sep.$key."=".urlencode($value);
					$sep="&";
				}
			}
		}
	}
	if (count($_POST)>0){
		foreach ($_POST as $key=>$value){
			if ($key!="acao3_cod0") {
				if (is_array($value)){
					$$key=$_REQUEST[$key];
					foreach($$key as $subkey=>$subvalue){
						$qs.=$sep.$key."[".$subkey."]"."=".urlencode($subvalue);
						$sep="&";
					}
				}else{
					$qs.=$sep.$key."=".urlencode($value);
					$sep="&";
				}
			}
		}
	}
	return $qs;
}

function chamaPrint(){
	$qs=getQS();
	$qs = str_replace("LAYOUT_FORCADO", "LAYOUT_FORCADO_OLD", $qs);
	?><a href="javascript:<?=cospeJSnovaJanela(montaHREF($_SERVER['PHP_SELF']."?LAYOUT_FORCADO=layout_impressao".($qs==""?"":"&".$qs)),720,520, "yes", "yes", "Print", "dependent=no,directories=no,location=no,toolbar=no,status=yes,menubar=yes")?>">Imprimir&nbsp;<img align="absmiddle" src="/images/icn_print_22x22.gif" width="16" height="16" alt="Esta página em formato de impressão" border="0"></a><?
}

function _getSingletonMapaSiteArrayRaiz($inclusive=false) {
	global $__s__MapaSiteArray;
	if (!$__s__MapaSiteArray) {
		//ss_timing_start("_getSingletonMapaSiteArrayRaiz");
		//$minhaLista = _getSingletonMapaSiteList();
		$minhaLista = &FACTORY::DIRETORIO_LIST();
		$__s__MapaSiteArray = $minhaLista->getArrayHierRaiz($inclusive);
		//ss_timing_stop("_getSingletonMapaSiteArrayRaiz");
	}
	return $__s__MapaSiteArray;
}

/* @TODO: Elmininar. Este singleton é NÃO-BOM */
function _getSingletonMapaSiteList() {
	global $__s__MapaSiteList;
	if (!$__s__MapaSiteList) {
		$__s__MapaSiteList = &FACTORY::DIRETORIO_LIST();
		$__s__MapaSiteList->getTodos("ORDER BY DIRE3_ORD");
	}
	return $__s__MapaSiteList;
}

function _getSingletonArrayLinearDiretorios() {
	global $__s__MapaSiteArrayLinear;
	if (!$__s__MapaSiteArrayLinear) {
		$fabrica = &FACTORY::DIRETORIO_LIST();
		$__s__MapaSiteArrayLinear = $fabrica->getArrayLinearDiretorios();
	}
	return $__s__MapaSiteArrayLinear;
}

function montaHidden($nolog=false) {
	global $acao, $_configUsePHPSession;
	$eca = "<input type=\"Hidden\" name=\"acao3_cod0\" value=\"".(@$acao->acai3_md5)."\" />\n";
	$eca2="";
	if (isset($_REQUEST["DEBUG_DB"]) && $_REQUEST["DEBUG_DB"]=="true")$eca2.="<input type=\"Hidden\" name=\"DEBUG_DB\" value=\"true\" />\n";
	if ($nolog) $eca2 .= "<input type=\"Hidden\" name=\"NO_LOG\" value=\"".$acao->getValidadorBypass($acao)."\" />\n";

	return (!@$_configUsePHPSession ? $eca : "").$eca2;
}
function montaHREFstrict($txt, $nolog=false, $umaAcao=null){
	global $acao, $_configDocDefault, $mailcenterCod, $_configUsePHPSession, $passaCache;
	if (!$umaAcao) $umaAcao=$acao;
	if (!@$_configUsePHPSession && @!$passaCache) {
		if (substr($txt, -1)=="/") $txt.=$_configDocDefault;
		$finalurl  = (substr_count($txt, "?") > 0 ? $txt."&" : $txt."?");
		$finalurl .= "acao3_cod0=$umaAcao->acai3_md5";
		$finalurl .= ((isset($_REQUEST["DEBUG_DB"]) && $_REQUEST["DEBUG_DB"]!="") ? "&DEBUG_DB=".$_REQUEST["DEBUG_DB"] : "");
		$finalurl .= ($nolog==true ? getQsNolog($umaAcao) : "");
		return $finalurl;
	} else {
		$xtra='';
		$xtra .= ((isset($_REQUEST["DEBUG_DB"]) && $_REQUEST["DEBUG_DB"]!="") ? "&DEBUG_DB=".$_REQUEST["DEBUG_DB"] : "");
		$xtra .= ($nolog==true ? "&".getQsNolog($umaAcao) : "");
		if (substr_count($txt, "?") > 0) $sep='&'; else $sep='?';
		if ($xtra!='') $txt.=$sep.$xtra;
		return $txt;
	}

}
function montaHREF($txt, $nolog=false, $umaAcao=null) {
	global $acao, $_configDocDefault, $mailcenterCod, $_configUsePHPSession, $passaCache;
	if (!$umaAcao) $umaAcao=$acao;
	if (strrpos($txt,"/")==strlen($txt)-1) $txt.=$_configDocDefault;
	if ($txt!=''){
		if ($txt[0]=="?") {
			$txt=basename($_SERVER['PHP_SELF']).$txt;
			//echo($txt."<br>");
		}else if ($txt[0]=='&'){
			$txt=basename($_SERVER['PHP_SELF'])."?".substr($txt, 1);
		}
	}
	if (!@$_configUsePHPSession && @!$passaCache) {
		$finalurl  = (substr_count($txt, "?") > 0 ? $txt."&" : $txt."?");
		$finalurl .= "acao3_cod0=$umaAcao->acai3_md5";
		$finalurl .= ((isset($_REQUEST["DEBUG_DB"]) && $_REQUEST["DEBUG_DB"]!="") ? "&DEBUG_DB=".$_REQUEST["DEBUG_DB"] : "");
		$finalurl .= ($nolog==true ? getQsNolog($umaAcao) : "");
		return $finalurl;
	} else {
		$sep = (substr_count($txt, "?") > 0 ? '&' : "?");
		$finalurl  = $txt;
		if (isset($_REQUEST["DEBUG_DB"]) && $_REQUEST["DEBUG_DB"]!=""){
			$finalurl .= $sep."DEBUG_DB=".$_REQUEST["DEBUG_DB"];
			$sep='&';
		}
		$finalurl .= ($nolog==true ? $sep.getQsNolog($umaAcao) : "");
		if ($finalurl==$txt && $sep=="?") $finalurl.=$sep;
		return $finalurl;
	}
}

function getQsNolog($umaAcao=null){
	global $acao;
	if (!$umaAcao) $umaAcao=$acao;
	return "&NO_LOG=".$umaAcao->getValidadorBypass($umaAcao);
}

function getVars(){
	foreach ($_REQUEST as $key=>$value){
		//echo $key."=".$value."<br>";
		eval ("\$".$key." = \$value;");
	}
}
function showVars(){
	foreach ($_REQUEST as $key=>$value){
		if (is_array($value)){
			echo "<div style=\"font-size:10px;font-family:Courier New\">". $key."=".print_r($value,true)."</div>";
		}else{
			echo "<div style=\"font-size:10px;font-family:Courier New\">". $key."=".$value."</div>";
		}
	}
}
function prettyObj($obj, $html=true, $return=false){
	$txt=print_r($obj, true);
	if ($html) $txt=str_replace("    "," - -", nl2br(htmlspecialchars($txt)));
	if ($return) return $txt; else echo $txt;
}
function geraSenha($numChar, $altaBaixa, $comNumeros, $comSimbolos){
	$gerada="";
	for ($i=0;$i<$numChar;$i++){
		$gerou=false;
		while (!$gerou){
			$charCode=rand(33,122);
			if ($comSimbolos!=true && ($charCode<48 || ($charCode>57 && $charCode<65) || ($charCode>90 && $charCode<97))){
				continue;
			}
			if ($comNumeros!=true && ($charCode>47 && $charCode<58)){
				continue;
			}
			if ($altaBaixa!=true && ($charCode>64 && $charCode<91)){
				continue;
			}
			$gerou=true;
		}
		$gerada.=chr($charCode);
	}
	return $gerada;
}
function cospeExigeTrocaSenha(){
	global $acao, $_configNomeSite, $layoutURL;
	$stack=getFuncStack();
	if (function_exists('especificaCospeExigeTrocaSenha')) return especificaCospeExigeTrocaSenha();
	//echo cospeFuncStack($stack)."<br>";
	//echo ((int)$acao->pessoa->logado)."---<br>";
?>
<div style="margin: auto; width:400px;height: 200px; border: solid 1px gray; padding:10px;">
	<table border="0" align="center" cellspacing="6" celpadding="0" style="font-family: 'Overpass', sans-serif;">
	<form name="exigeTrocaSenha" action="" method="post">
		<input type="hidden" name="_log_action" value="trocasenha">
		<?=montaHidden()?>
		<?
		$logo = realpath($_SERVER["DOCUMENT_ROOT"]."/../userfiles/logo.png");
		//var_dump($logo);
		if(file_exists($logo)){
?>
		<tr>
			<td colspan="2" align="center"><img border="0" src="/userfiles/logo.png" alt=""></td>
		</tr>
<?
		} else {
?>
		<tr>
			<td colspan="2" align="center"><? echo $_configNomeSite; ?></td>
		</tr>
<?
		}
?>
		<tr>
			<td colspan="2">&Eacute; necess&aacute;rio que voc&ecirc; altere sua senha provis&oacute;ria:<br/><br/></td>
		</tr>
		<tr>
			<td align="right">Nova senha:</td>
			<td><input type="text" name="newPassword1" value=""/><br/></td>
		</tr>
		<tr>
			<td align="right">Repita a senha:</td>
			<td><input type="text" name="newPassword2" value=""/><br/></td>
		</tr>
		<tr>
			<td>&nbsp;</td>
			<td><input type="submit" name="doTrocaSenha" value="Alterar"></td>
		</tr>
	</form>
	</table>
</div>

<?
}
function geraUsername($nome, $email=null){
	$pess=&FACTORY::PESSOA();
	$nome=str_replace('A/C', '', $nome);
	$nome=str_replace('-', '', $nome);
	$nome=str_replace('/', '', $nome);
	while (strpos($nome, '  ')!==false){
		$nome=str_replace('  ', ' ', $nome);
	}
	//echo((int)(strpos($nome, '  ')!==false)."<br>");
	$nacos=explode(" ", strtolower(trim($nome)));
	$pess2_username=trim($nacos[0]);
	if ($pess2_username=='')$pess2_username='usuario';
	//echo($pess2_username."<br>");
	$achou=false;
	if ($pess->getByLogin($pess2_username)){
		if (sizeOf($nacos)>1){

			for ($i=1; $i<sizeOf($nacos); $i++){
				$nacos[$i]=trim($nacos[$i]);
				if (@$nacos[$i][0]) {
					$inicial=$nacos[$i][0];
					$pess2_username.=$inicial;
				}else{
					//echo('Não achou a inicial no naco '.$i.' para o nome '.$nome."<br>");
				}
			}
			//echo($pess2_username."<br>");
			if (!$pess->getByLogin($pess2_username)){
				$achou=true;
			}else{
				//echo("já tinha<br>");
			}
		}
		if (!$achou){
			$pess2_username=$nacos[0];
			$cont=1;
			while ($pess->getByLogin($pess2_username)){
				$cont++;
				$pess2_username=$nacos[0].$cont;
			}
		}
	}
	return $pess2_username;
}



function cospeLoginGenerico($destino1=null){
	global $layoutURL;
	//phpinfo();
	//echo($layoutURL."==============================<br>");
	global $acao;
	if (!@$destino) $destino=$_SERVER["PHP_SELF"];
	if ($acao->pessoa->logado){
?>
	<table cellspacing="0" cellspacing="0" cellpadding="2">
		<tr>
			<td>Usuário:&nbsp;<b><a href="<?=montahref("/cadastro/index.php")?>"><?= $acao->pessoa->pess2_nom ?></a></b>&nbsp;&nbsp;&lt;&lt;&nbsp;<a href="<?=montahref("")."&_log_action=logoff"?>"><b>Sair</b></a></td><td>&nbsp;</td>
		</tr>
	</table>
<?
	}else{
?>
<script language="JavaScript">
function goEsqueciSenha(){
	gourl ='/<?= montaHREF("esquecisenha.php") ?>&LAYOUT_FORCADO=cnetserver_js';
	janela = 'janSenha'
	props = 'toolbar=no,location=no,directories=no,menubar=no,resizable=yes,scrollbars=yes,status=no,width=550,height=250,top=210,left=171'
	window.open(gourl,janela,props);
}
</script>
<table cellspacing="0" cellpadding="2" border="0">
	<form action="<?= $_SERVER["PHP_SELF"] ?>" method="post" name="formlogin">
	<?if ($acao->mensagem_login){?>
	<tr>
		<td colspan="5" style="font-size:8pt;font-weight:bold"><?= $acao->mensagem_login ?></td>
	</tr>
	<?}?>
	<tr>
		<td>Usuário</td>
		<td><input size="10" type="text" name="USERNAME" value="<?= $acao->pessoa->pess2_username ?>"></td>
		<td>Senha</td>
		<td><input size="10" type="password" name="PASSWORD"></td>
		<td><input type="submit" name="submeter" value="Entrar"><input type="hidden" name="_log_action" value="login"><?= montahidden() ?>
		<?= cospeForwardGetPost();?>
	</tr>
	<tr>
		<td>&nbsp;</td>
		<td colspan="4" valign="bottom"><a href="/esquecisenha.php">Esqueci minha senha</a><br><span style="font-size:10px">(Lembre-se: a senha e o nome de usuário são sensíveis à letras maiúsculas e minúsculas.)</span></td>
	</tr>
	</form>
</table>
<script language="JavaScript">
<?php
	if ($acao->pessoa->pess2_username) {
		?>//document.formlogin.PASSWORD.focus();
<?
	}else{
		?>//document.formlogin.USERNAME.focus();
<?
	}
?>
</script>
<?
	}
}

function cospeForwardGetPost(){
	global $aqui, $_configSanitizeInput;
	if (isset($_configSanitizeInput) && $_configSanitizeInput>0) $limpa=false; else $limpa=true;
	foreach ($_GET as $key=>$value){
		$value=cleanVarXss($value);
		$value=str_replace('"', '&quot;', $value);
		//echo $key."-".(int)($key!="acao3_cod0")." valor da key no foreach do get<br>";
		$arExcl=array("acao3_cod0", "USERNAME", "PASSWORD", "_log_action", "mace2_cod", "_origQS");
		if (!in_array($key, $arExcl)){
		//if ($key!="acao3_cod0" && $key!="USERNAME" && $key!="PASSWORD" && $key!="_log_action"){
			//echo "entrou<br>";
			if (is_array($value)){
				foreach($value as $chave=>$valor){
					?><input type="hidden" name="<?= $key ?>[<?=$chave?>]" value="<?= $valor ?>" />
<?
				}
			}else{
				if ($key=='?') continue;
				?><input type="hidden" name="<?= $key ?>" value="<?= $value ?>" />
<?
			}
		}
	}
	$qlp="";
	$glue="";
	foreach ($_POST as $key=>$value){
		$value=cleanVarXss($value);
		$value=str_replace('"', '&quot;', $value);
		//echo $key." valor da key no foreach do post<br>";
		$arExcl=array("acao3_cod0", "USERNAME", "PASSWORD", "_log_action", "mace2_cod", "_origQS");
		if (!in_array($key, $arExcl)){
		//if ($key!="acao3_cod0" && $key!="USERNAME" && $key!="PASSWORD" && $key!="_log_action" && $key!="_origQS"){
			if (is_array($value)){
				foreach($value as $chave=>$valor){
					?><input type="hidden" name="<?= $key ?>[<?=$chave?>]" value="<?= $valor ?>" />
<?
					$qlp.=$glue.$key."[".$chave."]=".urlencode($valor);
					$glue="&";
				}
			}else{
				if ($key=='?') continue;
				?><input type="hidden" name="<?= $key ?>" value="<?= $value ?>" />
<?
				$qlp.=$glue.$key."=".urlencode($value);
			}
			$glue="&";
		}
	}
	//echo nl2br(print_r($aqui,true))." ===<br/>";exit;
	$origqs=(@$aqui->queryLimpo==""?$qlp:$aqui->queryLimpo);
	if ($origqs=='?') $origqs='';
	$origqs=cleanVarXss($origqs);
	?><input type="hidden" name="_origQS" value="<?= $origqs ?>" />
<?
	//echo($qlp."<br>");
}

//function cospeLoginCnet2($pathLayout=null,$destino1=null){
function cospeLoginCnet2($pathLayout=null){
	global $layoutURL;
	if(!$pathLayout){
		$layoutURL=$layoutURL;
	} else {
		$layoutURL = $pathLayout;
	}
	global $acao;
	if (@!$destino) $destino=$_SERVER["PHP_SELF"];
	if ($acao->pessoa->logado){
	?>
	<table cellspacing="0" cellspacing="1">
		<tr>
			<td><img src="<?= $layoutURL ?>/images/l2_login.gif" alt="" width="45" height="16" border="0">&nbsp;<b><a href="<?=montahref("/cadastro/index.php")?>"><?= $acao->pessoa->pess2_nom ?></a></b>&nbsp;&nbsp;&lt;&lt;&nbsp;<a href="<?=montahref("")."&_log_action=logoff"?>"><b>Sair</b></a></td><td>&nbsp;</td>
		</tr>
	</table>
	<?
	}else{
	?>
<script language="JavaScript">
function goEsqueciSenha(){
	gourl ='/<?= montaHREF("esquecisenha.php") ?>&LAYOUT_FORCADO=cnetserver_js';
	janela = 'janSenha'
	props = 'toolbar=no,location=no,directories=no,menubar=no,resizable=yes,scrollbars=yes,status=no,width=550,height=250,top=210,left=171'
	window.open(gourl,janela,props);
}
</script>
<table cellspacing="0" cellpadding="2" border="0">
	<form action="<?= $_SERVER["PHP_SELF"] ?>" method="post" name="formlogin">
	<?if ($acao->mensagem_login){?>
	<tr>
		<td colspan="7" style="font-size:8pt;font-weight:bold"><?= $acao->mensagem_login ?></td>
	</tr>
	<?}?>
	<tr>
		<td><img align="absmiddle" src="<?= $layoutURL ?>/images/l2_login.gif" alt="" width="45" height="16" border="0"></td>
		<td><input size="10" type="text" name="USERNAME" value="<?= $acao->pessoa->pess2_username ?>"></td>
		<td><img src="<?= $layoutURL ?>/images/l2_senha.gif" alt="" width="38" height="16" border="0"></td>
		<td><input size="10" type="password" name="PASSWORD"></td>
		<td><input type="image" name="submeter" src="<?= $layoutURL ?>/images/l2_botlogin.gif" alt="Autenticar usuário" align="middle"><input type="hidden" name="_log_action" value="login"><?= montahidden() ?>
		<?= cospeForwardGetPost();?>
		<td valign="bottom">&nbsp;<a href="#" onclick="goEsqueciSenha();"><img src="<?= $layoutURL ?>/images/l2_esqueci.gif" alt="" width="39" height="16" border="0"></a></td>
		<td>&nbsp;</td>
	</tr>
	</form>
</table>
<script language="JavaScript">
	<?php
	if ($acao->pessoa->pess2_username) {
		?>//document.formlogin.PASSWORD.focus();
	<?
	}else{
		?>//document.formlogin.USERNAME.focus();
	<?
	}
	?>
</script>
<?
	}
}


function cospeLoginCvideo($destino=null){
	global $layoutURL;
	//phpinfo();
	//echo($layoutURL."==============================<br>");
	global $acao;
	if (!$destino) $destino=$_SERVER["PHP_SELF"];
	if ($acao->pessoa->logado){
?>
	<table cellspacing="0" cellspacing="1">
		<tr>
			<td><img src="<?= $layoutURL ?>/images/cv2_login.gif" alt="" width="45" height="14" border="0">&nbsp;<b><a href="<?=montahref("/cadastro/index.php")?>"><?= $acao->pessoa->pess2_nom ?></a></b>&nbsp;&nbsp;&lt;&lt;&nbsp;<a href="<?=montahref("")."&_log_action=logoff"?>"><b>Sair</b></a></td><td>&nbsp;</td>
		</tr>
	</table>
<?
	}else{
?>
<script language="JavaScript">
function goEsqueciSenha(){
	gourl ='/<?= montaHREF("esquecisenha.php") ?>&LAYOUT_FORCADO=cnetserver_js';
	janela = 'janSenha'
	props = 'toolbar=no,location=no,directories=no,menubar=no,resizable=yes,scrollbars=yes,status=no,width=550,height=250,top=210,left=171'
	window.open(gourl,janela,props);
}
</script>
<table cellspacing="0" cellpadding="2" border="0">
	<form action="<?= $_SERVER["PHP_SELF"] ?>" method="post">
	<tr>
	<?if ($acao->mensagem_login){?>
	<tr>
		<td colspan="7" style="font-size:8pt;font-weight:bold"><?= $acao->mensagem_login ?></td>
	</tr>
	<?}?>
	<tr>
		<td><img align="absmiddle" src="<?= $layoutURL ?>/images/cv2_login.gif" alt="" width="45" height="14" border="0"></td>
		<td><input size="10" type="text" name="USERNAME" value="<?= $acao->pessoa->pess2_username ?>"></td>
		<td><img src="<?= $layoutURL ?>/images/cv2_senha.gif" alt="" width="39" height="14" border="0"></td>
		<td><input size="10" type="password" name="PASSWORD"></td>
		<td><input type="image" name="submeter" src="<?= $layoutURL ?>/images/cv2_botlogin.gif" alt="Autenticar usuário" align="middle"><input type="hidden" name="_log_action" value="login"><?= montahidden() ?>
		<?= cospeForwardGetPost();?>
		<td valign="bottom">&nbsp;<a href="#" onclick="goEsqueciSenha();"><img src="<?= $layoutURL ?>/images/cv2_esquecisenha.gif" alt="" width="42" height="15" border="0"></a></td>
		<td>&nbsp;</td>
	</tr>
	</form>
</table>
<?
		}
}

function cospeLoginInter($destino=null, $layout=null, $lang='br' ){
	global $acao;
	if($lang == 'en' ){
		$txtuser = "User";
		$txtsenha = "Password";
		$txtexit = "Log off";
	}elseif($lang == 'es' ){
		$txtuser = "Usuario";
		$txtsenha = "Senha";
		$txtexit = "Salir";
	}else{
		$txtuser = "Usuário";
		$txtsenha = "Senha";
		$txtexit = "Sair";
	}
	if(!$layout) $layout='cnetserver_js';
	if (!$destino) $destino=$_SERVER["PHP_SELF"];
	if ($acao->pessoa->logado){
	?>
	<table cellspacing="0" cellspacing="1">
		<tr>
			<td style="font-size:12px"><?= $txtuser ?>:&nbsp;<b><a href="<?=montahref("/cadastro/index.php")?>"><?= $acao->pessoa->pess2_nom ?></a></b>&nbsp;&nbsp;&lt;&lt;&nbsp;<a href="<?=montahref("")."&_log_action=logoff"?>"><b><?= $txtsair ?></b></a></td><td>&nbsp;</td>
		</tr>
	</table>
<?
	}else{
?>
<script language="JavaScript">
function goEsqueciSenha(){
	gourl ='/<?= montaHREF("esquecisenha.php") ?>&LAYOUT_FORCADO=<?= $layout ?>';
	janela = 'janSenha'
	props = 'toolbar=no,location=no,directories=no,menubar=no,resizable=yes,scrollbars=yes,status=no,width=550,height=250,top=210,left=171'
	window.open(gourl,janela,props);
}
</script>
<table cellspacing="0" cellpadding="2" border="0">
	<form action="<?= $_SERVER["PHP_SELF"] ?>" method="post">
	<?if ($acao->mensagem_login){?>
	<tr>
		<td colspan="6" style="font-weight:bold"><?= $acao->mensagem_login ?></td>
	</tr>
	<?}?>
	<tr>
		<td style="font-size:12px">Login:&nbsp;</td>
		<td><input size="10" type="text" name="USERNAME" value="<?= $acao->pessoa->pess2_username ?>"></td>
		<td style="font-size:12px">&nbsp;<?= $txtsenha ?>:&nbsp;</td>
		<td><input size="10" type="password" name="PASSWORD"></td>
		<td><input type="submit" name="submeter" value="OK"><input type="hidden" name="_log_action" value="login"><?= montahidden() ?>
		<?= cospeForwardGetPost();?>
		<td style="font-size:8pt">&gt;<a href="javascript:goEsqueciSenha();"><b><?= $txtsenha ?></b></a>?</td>
	</tr>
	</form>
</table>
<?
		}
}

function cospeLogin($destino=null, $layout=null){
	//phpinfo();
	global $acao;
	if(!$layout) $layout='cnetserver_js';
	if (!$destino) $destino=$_SERVER["PHP_SELF"];
	if ($acao->pessoa->logado){
?>
	<table cellspacing="0" cellspacing="1">
		<tr>
			<td style="font-size:12px">Usuário:&nbsp;<b><a href="<?=montahref("/cadastro/index.php")?>"><?= $acao->pessoa->pess2_nom ?></a></b>&nbsp;&nbsp;&lt;&lt;&nbsp;<a href="<?=montahref("")."&_log_action=logoff"?>"><b>Sair</b></a></td><td>&nbsp;</td>
		</tr>
	</table>
<?
	}else{
?>
<script language="JavaScript">
function goEsqueciSenha(){
	gourl ='/<?= montaHREF("esquecisenha.php") ?>&LAYOUT_FORCADO=<?= $layout ?>';
	janela = 'janSenha'
	props = 'toolbar=no,location=no,directories=no,menubar=no,resizable=yes,scrollbars=yes,status=no,width=550,height=250,top=210,left=171'
	window.open(gourl,janela,props);
}
</script>
<table cellspacing="0" cellpadding="2" border="0">
	<form action="<?= $_SERVER["PHP_SELF"] ?>" method="post">
	<?if ($acao->mensagem_login){?>
	<tr>
		<td colspan="6" style="font-weight:bold"><?= $acao->mensagem_login ?></td>
	</tr>
	<?}?>
	<tr>
		<td style="font-size:12px">Login:&nbsp;</td>
		<td><input size="10" type="text" name="USERNAME" value="<?= $acao->pessoa->pess2_username ?>"></td>
		<td style="font-size:12px">&nbsp;Senha:&nbsp;</td>
		<td><input size="10" type="password" name="PASSWORD"></td>
		<td><input type="submit" name="submeter" value="OK"><input type="hidden" name="_log_action" value="login"><?= montahidden() ?>
		<?= cospeForwardGetPost();?>
		<td style="font-size:8pt">&gt;<a href="javascript:goEsqueciSenha();"><b>Senha</b></a>?</td>
	</tr>
	</form>
</table>
<?
		}
}

function cospeLoginFbidden($destino=null, $layout=null){
	//phpinfo();
	global $acao;
	if(!$layout) $layout='cnetserver_js';
	if (!$destino) $destino=$_SERVER["PHP_SELF"];
	if ($acao->pessoa->logado){
?>
	<div style="font-size:12px">Usuário:&nbsp;<b><a href="<?=montahref("/cadastro/index.php")?>"><?= $acao->pessoa->pess2_nom ?></a></b>&nbsp;&nbsp;&lt;&lt;&nbsp;<a href="<?=montahref("")."&_log_action=logoff"?>"><b>Sair</b></a></div>
	<div>&nbsp;</div>
<?
	}else{
?>
<script language="JavaScript">
function goEsqueciSenha(){
	gourl ='/<?= montaHREF("esquecisenha.php") ?>&LAYOUT_FORCADO=<?= $layout ?>';
	janela = 'janSenha'
	props = 'toolbar=no,location=no,directories=no,menubar=no,resizable=yes,scrollbars=yes,status=no,width=550,height=250,top=210,left=171'
	window.open(gourl,janela,props);
}
</script>
<div id="form-fbidden">
	<form action="<?= $_SERVER["PHP_SELF"] ?>" method="post">
	<?if ($acao->mensagem_login){?>

		<div id="logado"><?=$acao->mensagem_login;?></div>
	<?}?>

		<div class="form-fbidden-input"><input type="text" placeholder="Usuário" size="10" name="USERNAME" value="<?= $acao->pessoa->pess2_username ?>"></div>
		<div class="form-fbidden-input"><input type="password" placeholder="Senha" size="10" name="PASSWORD"></div>
		<button type="submit" class="btn btn-acessar" name="submeter">Acessar</button>
		<input type="hidden" name="_log_action" value="login"><?=montahidden();?>
		<?= cospeForwardGetPost();?>
	</form>
	<div id="esqueci-senha">&gt; <a href="javascript:goEsqueciSenha();">Esqueci minha senha</a>?</div>
</div>
<?
		}
}

function cospeLoginFbiddenEN($destino=null, $layout=null){
	//phpinfo();
	global $acao;
	if(!$layout) $layout='cnetserver_js';
	if (!$destino) $destino=$_SERVER["PHP_SELF"];
	if ($acao->pessoa->logado){
?>
	<div style="font-size:12px">Username:&nbsp;<b><a href="<?=montahref("/cadastro/index.php")?>"><?= $acao->pessoa->pess2_nom ?></a></b>&nbsp;&nbsp;&lt;&lt;&nbsp;<a href="<?=montahref("")."&_log_action=logoff"?>"><b>Logout</b></a></div>
	<div>&nbsp;</div>
<?
	}else{
?>
<script language="JavaScript">
function goEsqueciSenha(){
	gourl ='/<?= montaHREF("esquecisenha.php") ?>&LAYOUT_FORCADO=<?= $layout ?>';
	janela = 'janSenha'
	props = 'toolbar=no,location=no,directories=no,menubar=no,resizable=yes,scrollbars=yes,status=no,width=550,height=250,top=210,left=171'
	window.open(gourl,janela,props);
}
</script>
<div id="form-fbidden">
	<form action="<?= $_SERVER["PHP_SELF"] ?>" method="post">
	<?if ($acao->mensagem_login){?>

		<div id="logado"><span style="color:#cc0000;font-size:10px">Incorrect data (check uppercase and lowercase letters)</span></div>
	<?}?>

		<div class="form-fbidden-input"><input type="text" placeholder="Username" size="10" name="USERNAME" value="<?= $acao->pessoa->pess2_username ?>"></div>
		<div class="form-fbidden-input"><input type="password" placeholder="Password" size="10" name="PASSWORD"></div>
		<button type="submit" class="btn btn-acessar" name="submeter">Send</button>
		<input type="hidden" name="_log_action" value="login"><?=montahidden();?>
		<?= cospeForwardGetPost();?>
	</form>
	<div id="esqueci-senha">&gt; <a href="javascript:goEsqueciSenha();">Forgot my password</a>?</div>
</div>
<?
		}
}

function cospeFormBuscaCseek($det=false){
	global $seekit;
	if(!$det){ //não é o form detalhado para buscas mais complexas...
	?>
<script language="JavaScript">
function podeIr(){
	var busca;
	busca=document.buscaSimples.seekit;
	if (busca.value.length < 2){
		alert ("Faça a busca usando ao menos 2 caracteres, por favor.");
		return false;
	}else{
		return true;
	}
}
</script>
<table border="0" cellspacing="0" cellpadding="0">
	<form action="/cseek/index.php" name="buscaSimples" onsubmit="return podeIr();">
	<tr>
		<td><img usemap="#mapa" src="/images/tarjaformcseek.gif" alt="" width="369" height="24" border="0"></td><td><input align="absmiddle" size="15" type="text" name="seekit" id="seekit" value="<?= $seekit ?>"></td><td><input type="submit" name="v" value="&gt;&gt;"></td>
	</tr>
	<input type="hidden" name="action" value="buscaresult"><?= montaHidden()?>
	</form>
</table>
<map name="mapa">
<area alt="Acesso à área de cadastro" coords="10,3,75,22" href="<?=montaHref("/cadastro/")?>">
<area shape="RECT" coords="244,0,244,0">
<area alt="" coords="245,-2,361,23" href="<?=montaHref("/cseek/")?>">
</map>
	<?
	} else {


	}
}

//////////////////////////////////////////////////////////
//PRECISAVA ESTAR EM ALGUM LUGAR, POR ENQUANTO.
function cospeDocuGeral() {
	?>Documentos (geral)<?
}
//////////////////////////////////////////////////////////
//FUNÇÕES DO USO DO E-MAIL.
//////////////////////////////////////////////////////////
function initGetOut() {
	global $_tempOutputBuffer;
	$_tempOutputBuffer = ob_get_contents();
	@ob_end_clean();
	ob_start();
}

function endGetOut() {
	global $_tempOutputBuffer;
	$meuOutput = ob_get_contents();
	@ob_end_clean();
	ob_start();
	echo $_tempOutputBuffer;
	return $meuOutput;
}
function msg($txt, $agora=true){
	global $msgDebug;
	$msgDebug=@$msgDebug."<br>".nl2br($txt);
	if ($agora==true){
		echo "<div align=\"center\">".nl2br($txt)."</div>";
	}
}
function cospeMsg($txt){
	echo "<div align=\"center\" style=\"padding:10px;\">".$txt."</div><br>";
}

function getBean($qual, $vars=null){
	$umBean=&FACTORY::BEAN();
	if ($umBean->getMotor($qual, $vars)){
		return $umBean->motor;
	}
	return false;
}

function getArrayPessoasComAcesso($path){
	global $db;
	$arp2c=array();
	$sql="select pess2_cod, paro3_exib as exib, paro3_edit as edit
	from tb2_rope, tb3_paro
	where tb2_rope.role2_cod=tb3_paro.role2_cod
	and tb3_paro.path3_cod=?
";
	if (!$rec=$db->ExecuteBind($sql, array(array("Integer", $path->path3_cod)))){
			$db->_raiseError(__FILE__, __LINE__, $sql);
	}else{
		while (!$rec->EOF){
			$p2c=$db->tiraInteger($rec->fields[0]);
			$arp2c[$p2c]=(object)$p2c;
			$arp2c[$p2c]->exib=$db->tiraBit($rec->fields[1]);
			$arp2c[$p2c]->edit=$db->tiraBit($rec->fields[2]);
			$rec->MoveNext();
		}
	}
	$sql="select pess2_cod, diro3_exib as exib, diro3_edit as edit
	from tb2_rope, tb3_diro
	where tb2_rope.role2_cod=tb3_diro.role2_cod
	and tb3_diro.dire3_cod=?
";
	if (!$rec=$db->ExecuteBind($sql, array(array("Integer", $path->dire3_cod)))){
			$db->_raiseError(__FILE__, __LINE__, $sql);
	}else{
		while (!$rec->EOF){
			$p2c=$db->tiraInteger($rec->fields[0]);
			//echo($p2c."<br>");
			if (!isset($arp2c[$p2c])) {
				$arp2c[$p2c]=(object)$p2c;
				$arp2c[$p2c]->exib=$db->tiraBit($rec->fields[1]);
				$arp2c[$p2c]->edit=$db->tiraBit($rec->fields[2]);
				//echo(print_r($arp2c[$p2c], true)."<br>");
			}else{
				$arp2c[$p2c]->exib=($arp2c[$p2c]->exib & $p2c=$db->tiraBit($rec->fields[1]));
				$arp2c[$p2c]->edit=($arp2c[$p2c]->edit & $p2c=$db->tiraBit($rec->fields[2]));
			}
			$rec->MoveNext();
		}
	}
	$arPess=array();
	foreach($arp2c as $p2c=>$obj){
		$pess=&FACTORY::PESSOA();
		if ($pess->loadByPk($p2c)){
			$pess->exib=$obj->exib;
			$pess->edit=$obj->edit;
			$arPess[]=$pess;
		}
	}
	//echo(print_r($arPess, true)."<br>");
	return $arPess;
}

function initStyleAdmDocu() {
	global $__initStyleAdmDocuCuspido;
	if (@$__initStyleAdmDocuCuspido===true) return;
	?>
	<STYLE>
/********************** Config style administração de documentos *********/
	.tbAdmDocuSim {border:dotted 2px #ff9900;}
	.tbAdmRegSim {border:dotted 2px #000000;}
	.tbAdmMioloSim {border:dotted 2px #cc0000;}
	.tbAdmConteudoSim {border:dotted 2px #009900;}
	.tbAdmEldoSim {border:dotted 1px #ffff00; background-color:#f5f5f5;}

/********************** não sei o que é esse não **********************/
	.tbAdmDocuNao {
		border-style: none;
		border-width: 2px;
		border-color: transparent;
	}
	.tbAdmRegNao {
		border-style: none;
		border-width: 2px;
		border-color: transparent;
	}
	.tbAdmMioloNao {
		border-style: none;
		border-width: 2px;
		border-color: transparent;
	}
	.tbAdmConteudoNao {
		border-style: none;
		border-width: 2px;
		border-color: transparent;
	}
	.tbAdmEldoNao {
		border-style: none;
		border-width: 1px;
		border-color: transparent;
	}
/**********************************************************************/
	</STYLE>
	<?php
	$__initStyleAdmDocuCuspido=true;
}
function showSoData($ts, $tipoMes=1, $tipoAno=4){
	return showData($ts, $tipoMes, $tipoAno, false, false, false);
}
function showSoHora($ts){
	return showData($ts, 0,0);
}
function showData($ts, $tipoMes=1, $tipoAno=4, $ext=false, $hour=true, $seconds=true){
	//SE TIPOMES >>E<< TIPOANO FOREM ZERO SÓ É MOSTRADA A HORA
	global $oLocale;
	if (!$ts) return $ts;
	if ($tipoAno!=2 && $tipoAno!=4 && $tipoAno!==0)$tipoAno=4;
	if (is_array($ts)) $data=$ts; else $data=adodb_getDate($ts);
	$dia=substr("00".$data["mday"],-2);
	switch ($tipoMes){
		case 2:
			$mes=mesCurto($data["mon"]);
			break;
		case 3:
			$mes=mesLongo($data["mon"]);
			break;
		default:
		case 1:
			$mes=substr("00".$data["mon"],-2);
			break;
		case 0:
			$mes=null;
			$dia=null;
			break;
	}
	switch($tipoAno){
		case 0:
			$ano=null;
			break;
		case 4:
		case 2:
		default:
			$ano=substr($data["year"],-1 * $tipoAno);
			break;
	}
	if (isset($oLocale))$lingua=$oLocale; else $lingua="ptbr";
	switch ($oLocale){
		default:
		case "ptbr":
			$plusDia=($mes?($ext==true?" de ":"/"):'');
			$plusMes=($ano?($ext?" de ":"/"):'');
			//$ano=substr($data["year"],-1 * $tipoAno);
			$resp=$dia.$plusDia.$mes.$plusMes.$ano;
			if ($hour)$resp.=" ".substr("00".$data["hours"],-2).":".substr("00".$data["minutes"],-2).($seconds?":".substr("00".$data["seconds"],-2):"");
			break;
		case "enus":
			$plusDia=($mes?($ext==true?", ":"/"):'');
			$plusMes=($ano?($ext?", ":"/"):'');
			//$ano=substr($data["year"],-1 * $tipoAno);
			$resp=$mes.$plusMes.$dia.$plusDia.$ano;
			if ($hour)$resp.=" ".substr("00".$data["hours"],-2).":".substr("00".$data["minutes"],-2).":".($seconds?substr("00".$data["seconds"],-2):'');
			break;
	}
	return $resp;
}

function mesLongo($mes, $lang='br'){
	switch($lang){
		case 'es':
			$meses=Array("","Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Deciembre");
			break;
		default:
			$meses=Array("","Janeiro","Fevereiro","Março","Abril","Maio","Junho","Julho","Agosto","Setembro","Outubro","Novembro","Dezembro");
	}
	return $meses[$mes];
}

function mesCurto($mes, $lang='br'){
	switch($lang){
		case 'es':
			$meses=Array("","ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic");
			break;
		default:
			$meses=Array("","jan","fev","mar","abr","mai","jun","jul","ago","set","out","nov","dez");
	}
	return $meses[$mes];
}

function semanaCurto($data){
	$ds=array("dom", "seg", "ter", "qua", "qui", "sex", "sab");
	if (is_numeric($data)){
		$data=adodb_getdate($data);
	}
	return $ds[$data['wday']];
}

function semanaLongo($data, $completo=false){
	if (is_numeric($data)){
		$data=adodb_getdate($data);
	}
	return semanaLongoByWday($data['wday'], $completo);
}

function semanaLongoByWday($wday, $completo=false){
	if ($completo){
		$ds=array("Domingo", "Segunda-feira", "Terça-feira", "Quarta-feira", "Quinta-feira", "Sexta-feira", "Sábado");
	}else{
		$ds=array("Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado");
	}
	return $ds[$wday];
}

function getmicrotime() {
	$temparray=explode(" ",microtime());
	$returntime=$temparray[0]+$temparray[1];
	return $returntime;
}

function logaLinkMailcenter(){
	global $_configNomeSite;
	$debug=false;
	$mace2_cod=$_REQUEST["mace2_cod"];
	if (is_numeric($mace2_cod)==false) return;
	$pess2_cod=$_REQUEST["pess2_cod"];
	if (is_array($pess2_cod)) $pess2_cod=current($pess2_cod);
	if (is_numeric($pess2_cod)==false) return;
	$url=$_REQUEST["url"];
	if ($url){
		$temDados=false;
		if ($debug) echo($pess2_cod."<br>");
		if ($pess2_cod!="" && $pess2_cod!="_@pess2_cod@_"){
			//echo("Vou logar pessoa"."<br>");
			if ($pess2_cod==0){
				$temDados=true;
			}else if ($mace2_cod && $pess2_cod){
				//LOGA LINK EXTERNO MAILCENTER IDENTIFICADO
				$log=&FACTORY::LOG_REDIRECIONAMENTO_MAILCENTER_LINK_EXTERNO();
				$log->mace2_cod=$mace2_cod;
				$log->pess2_cod=$pess2_cod;
				$log->rmce2_dat_hr=time();
				$log->rmce2_url=$url;
				//echo $pess2_cod." - ".$mace2_cod." - ".$_REQUEST["mace2_cod"]."<br>";
				$log->insert();
				$temDados=true;
			}
		} else if ($_REQUEST["lenc2_cod"]){
			$lenc2_cod=$_REQUEST["lenc2_cod"];
			if (is_array($lenc2_cod)) $lenc2_cod=current($lenc2_cod);
			if ($lenc2_cod!='_@lenc2_cod@_'){
				//echo("Vou logar externo avulso"."<br>");
				//LOGA LINK EXTERNO MAILCENTER NÃO CADASTRADO
				$log=&FACTORY::LOG_REDIRECIONAMENTO_LISTA_AVULSA_LINK_EXTERNO();
				$log->lenc2_cod=$lenc2_cod;
				$log->rave2_dat_hr=time();
				$log->rave2_url=$url;
				$log->insert();
			}
			$temDados=true;
		}else{
			$temDados=true;
		}
		if ($temDados){
			$ancora='';
			if (strpos($url, "#")!==false){
				$nacos=explode("#", $url);
				$url=$nacos[0];
				$ancora=$nacos[1];
			}
			if (strpos($url, "frame.asp?page")===false){
				if (strpos($url, "sf=1")!==false){
					if (strpos($url, "?sf=1")!==false){
						$ini=strpos($url, "?sf=1");
						if (strpos($url, "&", $ini)===false){
							$url=str_replace("?sf=1", "", $url);
						}else{
							$url=str_replace("sf=1&", "", $url);
						}
					}
				}else{
					if (strpos($url, "?")===false) $url.="?";else $url.="&";
					$url.="from=".urlencode($_configNomeSite).($ancora!=''?"#".$ancora:'');
				}
			}
			header("Location: ".$url);
			exit;
		}
	}else{
		if ($_REQUEST["_log_action"]!="login"){
			if ($pess2_cod!="_@pess2_cod@_"){
				if ($mace2_cod && $pess2_cod){
					if ($debug) echo($mace2_cod." - ".$pess2_cod."<br>");
					//LOGA RETORNO MAILCENTER IDENTIFICADO
					$log=&FACTORY::LOG_DE_RETORNO_MAILCENTER();
					$log->mace2_cod=$mace2_cod;
					$log->pess2_cod=$pess2_cod;
					$log->lrmc2_dat_hr=time();
					$url=str_replace("mace2_cod=".$mace2_cod."&pess2_cod=".$pess2_cod."&lenc2_cod=", "", $_SERVER["REQUEST_URI"]);
					$log->lrmc2_url=$url;
					//echo str_replace("mace2_cod=".$mace2_cod."&pess2_cod=".$pess2_cod."&lenc2_cod=", "", $_SERVER["REQUEST_URI"])."<br>";
					//exit;
					$log->insert();
				} else if ($_REQUEST["lenc2_cod"]){
					//LOGA RETORNO MAILCENTER NÃO CADASTRADO
					$log=&FACTORY::LOG_DE_RETORNO_NAO_CADASTRADO();
					$log->lenc2_cod=$_REQUEST['lenc2_cod'];
					$log->lrnc2_dat_hr=time();
					$url=str_replace("mace2_cod=".$mace2_cod."&pess2_cod=".$pess2_cod."&lenc2_cod=".$_REQUEST['lenc2_cod'], "", $_SERVER["REQUEST_URI"]);
					$log->lrnc2_url=$url;
					$log->insert();
				}
			}
		}
	}
}

function logaLinkExterno(){
	global $_configNomeSite;
	$modoRedir=$_REQUEST["redir"];
	if ($modoRedir=="le"){
		$referer=$_SERVER['HTTP_REFERER'];
		$host=$_SERVER['HTTP_HOST'];
		if (strpos($referer, $host)===false) exit;
		if ($url=$_REQUEST["url"]){
			$log=null;
			 if ($acaoCod=$_REQUEST["acai3_cod"]){
				if (!is_numeric($acaoCod)) exit;
				$acai=&FACTORY::ACAO_IMEDIATA();
				if (!$acai->loadByPk($_REQUEST["acai3_cod"])) exit;

				//LOGA REDIRECIONAMENTO PARA LINK EXTERNO
				$log=&FACTORY::LOG_REDIRECIONAMENTO_PARA_LINK_EXTERNO();
				$log->rdle3_dat_hr=time();
				$log->rdle3_url=substr($url, 0, 400);
				$log->acai3_cod=$acaoCod;
				$brow=&FACTORY::BROWSER();
				$brow->getBrowser();
				if ($brow->brow3_spy==true) $log=null;
			}
			//REDIRECIONA
			$ancora='';
			if (strpos($url, "#")!==false){
				$nacos=explode("#", $url);
				$url=$nacos[0];
				$ancora=$nacos[1];
			}
			if (strpos($url, "sf=1")!==false){
				if (strpos($url, "?sf=1")!==false){
					$ini=strpos($url, "?sf=1");
					if (strpos($url, "&", $ini)===false){
						$url=str_replace("?sf=1", "", $url);
					}else{
						$url=str_replace("sf=1&", "", $url);
					}
				}
			}else{
				if (strpos($url, "?")===false) $url.="?";else $url.="&";
				$url.="from=".urlencode($_configNomeSite).($ancora!=''?"#".$ancora:'');
			}
			$purl=parse_url($url);
			$valid=true;
			if ($purl===false) $valid=false;
			if (strpos($url,"\r")!==false || strpos($url,"\n")!==false) $valid=false;
			if ($valid==true){
				if ($log) @$log->insert();
				header("Location: ".$url);
			}
			exit;
		}else{
			//echo "Link não fornecido.<br>";
			exit;
		}
	}
}

function isdate($umaData){
	$umaData=dateToTimestamp($umaData);
	if ($umaData){
		$umaData=getdate($umaData);
		return checkdate($umaData['mon'],$umaData['mday'], $umaData['year']);
	}else{
		return false;
	}
}

function dateToTimestamp($data){
	if (!strpos($data," ")===false){
		$arData=explode(" ", $data);
		$data=$arData[0];
		$horas=$arData[1];
	}
	//echo $data.".........<br>";
	if (@$horas){
		$arData=explode(":", $horas);
		$hora=$arData[0];
		$minuto=$arData[1];
		if (count($arData)<3) $segundo=0; else $segundo=$arData[2];
	}
	if (@!$hora) $hora=0;
	if (@!$minuto) $minuto=0;
	if (@!$segundo) $segundo=0;
	$arData = explode("/", $data);
	krsort($arData);
	if (@$arData[2]){
		$ano=$arData[2];
		$mes=$arData[1];
		$dia=$arData[0];
	}else{
		return null;
	}
	if (!$mes) $mes=1;
	if ($mes>12) $mes=12;
	if (!$dia) $dia=1;
	if (checkdate($mes, $dia, $ano)){
		//echo(mktime($hora, $minuto, $segundo, $mes, $dia, $ano)." mktime<br>");
		//echo(adodb_mktime($hora, $minuto, $segundo, $mes, $dia, $ano)." adodb<br>");
		//exit;
		return adodb_mktime($hora, $minuto, $segundo, $mes, $dia, $ano);
	}else{
		return null;
	}
}

function dateadd($part, $n, $data, $cospeTS=false){
	if(!is_array($data)){
		$data = adodb_getdate($data);
	}
	switch (strtolower($part)){
		case "day":
		case "d":
			$data['mday']=$data['mday'] + $n;
			break;
		case "month":
		case "mon":
		case "m":
			$data['mon']=$data['mon'] + $n;
			$corrUltMes=false;
			while ($data['mon']<1){
				$data['mon']+=12;
				$data['year']--;
			}
			while ($data['mon']>12){
				$data['mon']-=12;
				$data['year']++;
			}
			if ($data['mon']==2 && $data['mday']>28){
				$td=getdate(dateToTimestamp('01/03/'.$data['year']));
				$td2=dateadd('d',-1,$td);
				if ($data['mday']>$td2['mday']) $data['mday']=$td2['mday'];
			}
			if ($data['mday']==31){
				$corrUltMes=true;
				$data['mday']=1;
				$data['mon']++;
			}
			if ($corrUltMes)$data=dateadd('d', -1, $data);
			break;
		case "year":
		case "yyyy":
		case "a":
		case "y":
			$data['year']=$data['year'] + $n;
			break;
		case "h":
			$data['hours'] = $data['hours'] + intval($n);
			break;
		case "n":
			$data['minutes'] = $data['minutes'] + intval($n);
			break;
		case "s":
			//echo $data[seconds]." antes<br>";
			$data[seconds]=$data[seconds] + $n;
			//echo $data[seconds]." depois<br>";
			break;
		default:
			trigger_error ("Date part not specified: ".$part, E_USER_WARNING);
	}
	//echo date("dmY", mktime($data))."<br>";
	$timestamp =  getTS($data);//mktime($data['hours'], $data['minutes'], $data['seconds'], $data['mon'], $data['mday'], $data['year']);
	if($cospeTS){
		return $timestamp;
	}
	return adodb_getdate($timestamp);
}

function datediff($part, $data1, $data2){
	switch (strtolower($part)){
		case "d":
			$uData1=mktime(0, 0, 0, $data1["mon"], $data1["mday"], $data1["year"]);
			$uData2=mktime(0, 0, 0, $data2["mon"], $data2["mday"], $data2["year"]);
			$dif=$uData1-$uData2;
			$dif=$dif/86400;
			break;
		case "m":
			$dif=($data1["year"]-$data2["year"])*12+$data1["mon"]-$data2["mon"];
			break;
		case "a":
		case "y":
		case "yyyy":
			$dif=$data1["year"] - $data2["year"];
			break;
		case "h":
			$uData1=mktime($data1["hours"], 0, 0, $data1["mon"], $data1["mday"], $data1["year"]);
			$uData2=mktime($data2["hours"], 0, 0, $data2["mon"], $data2["mday"], $data2["year"]);
			$dif=$uData1-$uData2;
			$dif=$dif/3600;
			break;
		case "n":
			$uData1=mktime($data1["hours"], $data1["minutes"], 0, $data1["mon"], $data1["mday"], $data1["year"]);
			$uData2=mktime($data2["hours"], $data2["minutes"], 0, $data2["mon"], $data2["mday"], $data2["year"]);
			$dif=$uData1-$uData2;
			$dif=$dif/60;
			break;
	}
	return $dif;
}

function getTS($date){
	$ret = adodb_mktime ($date['hours'], $date['minutes'], $date['seconds'], $date['mon'], $date['mday'], $date['year']);
	return $ret;
}

/////////////////////////////////////////////////////////////////////////////
// Classes do e-gineer! (Muito boas...)
/////////////////////////////////////////////////////////////////////////////

$ss_log_level = 0;
$ss_log_filename = 'F:\temp\eternit_log';
$ss_log_levels = array(
    "NONE"  => 0,
    "ERROR" => 1,
    "INFO"  => 2,
    "DEBUG" => 3);

function ss_log_set_level ($level = ERROR) {
    global $ss_log_level;
    $ss_log_level = $level;
}

function ss_log ($level, $message) {
    global $ss_log_level, $ss_log_filename;
    if ($ss_log_levels[$ss_log_level] < $ss_log_levels[$level]) {
        // no logging to be done
        return false;
    }
    $fd = fopen($ss_log_filename, "a+");
    fputs($fd, $level.' - ['.ss_timestamp_pretty().'] - '.$message."\n");
    fclose($fd);
    return true;
}

function ss_log_reset () {
    global $ss_log_filename;
    @unlink($ss_log_filename);
}

function ss_timestamp_pretty() {
	return date("d M, Y H:i:s");
}

// Funcoes de timing... modificadas...

function ss_timing_start ($name = 'default') {
    global $ss_timing_start_times;
    $ss_timing_start_times[$name] = explode(' ', microtime());
}

function ss_timing_stop ($name = 'default') {
    global $ss_timing_stop_times;
    $ss_timing_stop_times[$name] = explode(' ', microtime());
}

function ss_timing_current ($name = 'default') {
    global $ss_timing_start_times, $ss_timing_stop_times;
    if (!isset($ss_timing_start_times[$name])) {
        return 0;
    }
    if (!isset($ss_timing_stop_times[$name])) {
        $stop_time = explode(' ', microtime());
    }
    else {
        $stop_time = $ss_timing_stop_times[$name];
    }
    $current = $stop_time[1] - $ss_timing_start_times[$name][1];
    $current += $stop_time[0] - $ss_timing_start_times[$name][0];
	?> <HR>Até aqui, o timing '<?=$name?>' levou <?=$current?> segundos.<HR><?php
}

function get_ss_timing_current ($name = 'default') {
    global $ss_timing_start_times, $ss_timing_stop_times;
    if (!isset($ss_timing_start_times[$name])) {
        return 0;
    }
    if (!isset($ss_timing_stop_times[$name])) {
        $stop_time = explode(' ', microtime());
    }
    else {
        $stop_time = $ss_timing_stop_times[$name];
    }
    $current = $stop_time[1] - $ss_timing_start_times[$name][1];
    $current += $stop_time[0] - $ss_timing_start_times[$name][0];
	return $current;
}

/////////////////////////////////////////////////////////////////////////////
// Fim das classes do e-gineer...
/////////////////////////////////////////////////////////////////////////////

// uma funcaozinha para cuspir todos os timings!

function getTabelaSSTiming() {
	global $ss_timing_start_times, $ss_timing_stop_times;
	$geral = get_ss_timing_current("pag_toda");
	?>
	<TABLE border="1" cellpadding="2" cellspacing="0">
	<TR style="font-weight: bold;">
		<TD>Timing
		</TD>
		<TD>Tempo
		</TD>
		<TD>Pcnt
		</TD>
	</TR>
	<?php
	foreach ($ss_timing_start_times as $nometiming => $valor) {
		$valor = get_ss_timing_current($nometiming);
		$percent = $valor * 100 / $geral;
		?>
		<TR>
			<TD><?=$nometiming?>
			</TD>
			<TD><?=$valor?>
			</TD>
			<TD><?=$percent>10?"<B>":""?><?=$percent?>%<?=$percent>10?"</B>":""?>
			</TD>
		</TR>
		<?php
	}
	?>
	</TABLE>
	<?php
}

function validateMail($email, $verificaDominio=true, $verificaUsuario=true){
	global $HTTP_HOST;
	/*
	$result[0] contém um booleano indicando true somente em caso de confirmação do servidor
	$result[1] contém uma string com a mensagem do resultado
	$result[2] contém um booleano indicando true quando não se pode descartar o e-mail (servidor não disponível ou recusou-se a responder)
	$result[3] contém um integer que segue a tabela abaixo:
	0 - e-mail válido e confirmado
	1 - e-mail válido e não confirmado
	2 - e-mail de domínio inválido
	3 - e-mail rejeitado pelo servidor
	4 - e-mail mal formado
	$result[4] retorna um formato alternativo que seja válido, quando encontrado, ou o e-mail original
	*/
	$translate_from=	'áàâãéêíóôõúç,()?!/\\´`~^¨*';
	$translate_to=		'aaaaeeiooouc              ';
	$result[4]=$email;
	if ($verificaUsuario)$verificaDominio=true;
	$testando=false;
	$result = array();
	$result[4]=$email;
	$re="^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,3})$";
	if (!eregi($re, $email)) {
		$result[4]="";
		$altEmail=trim($email);
		if (eregi($re, $altEmail)) {
			$result[4]=$altEmail;
		}else{
			//echo htmlspecialchars($altEmail)."<br>";
			if (substr($altEmail,-1)==".")$altEmail=substr($altEmail, 0, -1);
			if (substr($altEmail,-1)==";")$altEmail=substr($altEmail, 0, -1);
			if (substr($altEmail,-1)==":")$altEmail=substr($altEmail, 0, -1);
			$altEmail=strtr($altEmail, $translate_from, $translate_to);
			$altEmail=str_replace(" ", "", $altEmail);
			//echo htmlspecialchars($altEmail)."<br>";
			if (eregi($re, $altEmail)) {
				$result[4]=$altEmail;
			}
		}
		$result[0]=false;
		$result[1]=$email." não tem um formato válido.";
		$result[2]=false;
		$result[3] = 4;
		return $result;
    }
	if (!$verificaDominio){
		$result[0]=true;
		$result[1]=$email." tem formato válido.";
		$result[2]=true;
		$result[3] = 0;
		return $result;
	}
	list ( $Username, $Domain ) = split ("@",$email);
	if (getmxrr($Domain, $MXHost, $MXWeight)) {
		if ($testando) print_r($MXHost);
		if ($testando) print_r($MXWeight);
		foreach($MXHost as $key=>$umHost){
			$ordHosts[$MXWeight[$key]]=$umHost;
		}
		ksort($ordHosts);
		reset($ordHosts);
		$ConnectAddress=current($ordHosts);
		if (!$ConnectAddress) $ConnectAddress = $MXHost[0];
		if ($testando) echo $ConnectAddress." MX<br>";
	} else {
		$ConnectAddress = $Domain;
	}
	if ($verificaUsuario) {
		$Connect = fsockopen ( $ConnectAddress, 25, $errNumber, $errText, 15);
	    if ($Connect) {
			if (preg_match("/^220/", $Out = fgets($Connect, 1024))) {
				$toput="HELO $HTTP_HOST\r\n";
				if ($testando) echo htmlspecialchars($toput)." &gt;<br>";
				fputs ($Connect, $toput);
				$Out = fgets ( $Connect, 1024 );
				if ($testando) echo $Out." &lt;<br>";
				$toput="MAIL FROM: <{$email}>\r\n";
				if ($testando) echo htmlspecialchars($toput)." &gt;<br>";
				fputs ($Connect, $toput);
				$From = fgets ( $Connect, 1024 );
				if ($testando) echo $From." &lt;<br>";
				$toput="RCPT TO: <{$email}>\r\n";
				if ($testando) echo htmlspecialchars($toput)." &gt;<br>";
				fputs ($Connect, $toput);
				$To = fgets ($Connect, 1024);
				if ($testando) echo $To." &lt;<br>";
				$toput="QUIT\r\n";
				if ($testando) echo htmlspecialchars($toput)." &gt;<br>";
				fputs ($Connect, $toput);
				fclose($Connect);
				if (!ereg ("^250", $From) || !ereg ( "^250", $To )) {
					$result[0]=false;
					$result[1]="E-mail rejeitado pelo servidor.";
					$result[2]=false;
					$result[3] = 3;
					return $result;
				}else{
					$result[0]=true;
					$result[1]="$email é um endereço válido e ativo.";
					$result[2] = true;
					$result[3] = 0;
					return $result;
				}
			} else {
				$result[0] = false;
				$result[1] = "Não foi possível verificar a autenticidade do e-mail.";
				$result[2] = true;
				$result[3] = 1;
				return $result;
			}
		}
	}
	if ($verificaDominio){
		if(checkdnsrr($ConnectAddress,"ANY")){
			$result[0]=false;
			$result[1]="Não foi possível conectar-se ao servidor para verificar a autenticidade.";
			$result[2] = true;
			$result[3] = 1;
			//echo $errNumber." ".$errText."<br>";
			return $result;
		}else{
			$result[0]=false;
			$result[1]="Servidor não existente.";
			$result[2] = false;
			$result[3] = 2;
			//echo $errNumber." ".$errText."<br>";
			return $result;
		}
	}
	$result[0]=true;
	$result[1]="$email é um endereço válido e ativo.";
	$result[2] = true;
	$result[3] = 0;
	return $result;
}

function contador($oque="pv", $comSpider=true){
	global $db;
	$debug=false;
	$antes = getmicrotime();
	$oque=strtolower($oque);
	switch ($oque){
		case "cook":
			if ($comSpider){
				$sql="SELECT COUNT(DISTINCT TB3_COOK.COOK3_COD) AS COOKIES
				FROM TB3_COOK, TB3_SESS
				WHERE TB3_SESS.COOK3_COD=TB3_COOK.COOK3_COD
				";
				$sql="SELECT COUNT(TB3_COOK.COOK3_COD) AS COOKIES
				FROM TB3_COOK
				";
				$params=array();
			}else{
				$sql="SELECT COUNT(DISTINCT TB3_COOK.COOK3_COD) AS COOKIES
				FROM TB3_COOK, TB3_SESS
				WHERE TB3_SESS.COOK3_COD=TB3_COOK.COOK3_COD
				AND EXISTS(
					SELECT TB3_BROW.BROW3_COD
					FROM TB3_BROW
					WHERE TB3_BROW.BROW3_COD=TB3_SESS.BROW3_COD
					AND (TB3_BROW.BROW3_SPY IS NULL
					OR TB3_BROW.BROW3_SPY=?)
				)
				";
				$params=array(array("Bit", false));
			}
			if ($debug) echo $sql."<br>";
			if ($rec = $db->ExecuteBind($sql, $params)) {
				$obj = $rec->FetchNextObject();
				$depois = getmicrotime();
				if ($debug) echo ($depois-$antes)."<br>";
				return  $obj->COOKIES;
			}
			return false;

			break;
		case "sess":
			if ($comSpider){
				$sql="SELECT COUNT(SESS3_COD) AS SESSOES
				FROM TB3_SESS
				";
				$params=array();
			}else{
				$sql="SELECT COUNT(DISTINCT SESS3_COD) AS SESSOES
				FROM TB3_SESS, TB3_BROW
				WHERE TB3_SESS.BROW3_COD=TB3_BROW.BROW3_COD
				AND (TB3_BROW.BROW3_SPY IS NULL
				OR TB3_BROW.BROW3_SPY=?)
				";
				$params=array(array("Bit", false));
			}
			if ($debug) echo $sql."<br>";
			if ($rec = $db->ExecuteBind($sql, $params)) {
				$obj = $rec->FetchNextObject();
				$depois = getmicrotime();
				if ($debug) echo ($depois-$antes)."<br>";
				return  $obj->SESSOES;
			}
			return false;

			break;
		case "acai":
		case "pv":
		default:
			if ($comSpider){
				$sql="
				SELECT COUNT(ACAI3_COD) AS PAGEVIEWS
				FROM TB3_ACAI
				";
				$params=array();
			}else{
				$sql="
				SELECT COUNT(DISTINCT ACAI3_COD) AS PAGEVIEWS
				FROM TB3_ACAI, TB3_SESU, TB3_SESS, TB3_BROW
				WHERE TB3_ACAI.SESU3_COD=TB3_SESU.SESU3_COD
				AND TB3_SESU.SESS3_COD=TB3_SESS.SESS3_COD
				AND TB3_SESS.BROW3_COD=TB3_BROW.BROW3_COD
				AND (TB3_BROW.BROW3_SPY IS NULL
				OR TB3_BROW.BROW3_SPY=?)
				";
				$params=array(array("Bit", false));
			}
			if ($debug) echo $sql."<br>";
			if ($rec = $db->ExecuteBind($sql, $params)) {
				$obj = $rec->FetchNextObject();
				$depois = getmicrotime();
				if ($debug) echo ($depois-$antes)."s<br>";
				return  $obj->PAGEVIEWS;
			}
			return false;

			break;
	}
}

function cospeGrana($val, $comCifrao=false, $pais="BR"){
	switch ($pais){
		case "BR":
			return ($comCifrao?"R$ ":"").number_format($val, 2, ',', '.');
			break;
	}
}

function getSingletonMidias(){
	global $_s_singletonMidias;
	if ($_s_singletonMidias) return $_s_singletonMidias;
	$_s_singletonMidias=array();
	$listaMidia = &FACTORY::MIDIA_LIST();
	$listaMidia->getTodos("order by midi1_nom");
	while ($umaMidia=$listaMidia->getNextItem()) {
		$umaMidia->setNomeClasse();
		$_s_singletonMidias[$umaMidia->midi1_cod]=$umaMidia;
	}
	return $_s_singletonMidias;
}

function getSingletonEstilos(){
	global $_s_singletonEstilos;
	if ($_s_singletonEstilos) return $_s_singletonEstilos;
	$_s_singletonEstilos=array();
	$listaEstilos = &FACTORY::ESTILO_LIST();
	$listaEstilos->getTodos("order by esti1_nom");
	while ($umEstilo=$listaEstilos->getNextItem()) {
		$_s_singletonEstilos[$umEstilo->esti1_cod]=$umEstilo;
	}
	return $_s_singletonEstilos;
}

function emailRapido($to, $subj, $text, $html=null, $from=null, $rr=false, $cc=null, $bcc=null){
	global $_configEmailContato, $_configNomeEmailContato, $_configDominio, $_configUsaPHPMailer;
	require_once('codebase/function/classes_email.inc');
	if (@$_configUsaPHPMailer){
		require_once 'codebase/php/PHPMailer/src/Exception.php';
		require_once 'codebase/php/PHPMailer/src/PHPMailer.php';
		require_once 'codebase/php/PHPMailer/src/SMTP.php';
		global $SERVER_NAME, $_configSMTPHost, $_configSMTPPort, $_configSMTPHelo, $_configTimeoutSmtp, $_configSMTPUser, $_configSMTPPass, $_configSMTPDebug, $_configNomeEmailContato;
		if ($from){
			if (is_array($from)){
				$fromName=$from['nome'];
				$fromMail=$from['email'];
			}elseif (@strtolower(get_class($from))=='pessoa'){
				$fromName=$from->pess2_nom;
				$fromMail=$from->pess2_email;
			}else{
				$fromName=$from;
				$fromMail=$from;
			}
		}else{
			$fromName=$_configNomeEmailContato;
			$fromMail=$_configEmailContato;
		}
		$arTo=array();
		if (is_array($to)){
			if (isset($to['nome'])){
				$toName=$to['nome'];
				$toMail=$to['email'];
				$arTo[]=$toMail;
			}else{
				if (isset($to[0]['nome'])){
					$toName=$to[0]['nome'];
					$toMail=$to[0]['email'];
				}else{
					$toName=$to[0];
					$toMail=$to[0];
				}
				$arTo=$to;
			}
		}elseif (is_object($to) && @strtolower(get_class($to))=='pessoa'){
			$toName=$to->pess2_nom;
			$toMail=$to->pess2_email;
			$arTo[]=$toMail;
		}else{
			$toName=$to;
			$toMail=$to;
			$arTo[]=$toMail;
		}
		//echo nl2br(print_r($arTo, true))."<br/>";exit;
		$mailer = new CONSTRUNET_MAILER(null, null, null, null, true);
		$mailer->html = $html;
		$mailer->text = $text;
		$mailer->subject = $subj;
		$mailer->returnpath = $fromMail;
		$mailer->de_nome = $fromName;
		$mailer->de_email = $fromMail;
		$mailer->assunto = $subj;
		$mailer->para_nome = $toName;
		$mailer->para_email = $toMail;
		$mailer->arraypara = $arTo;
		//echo nl2br(print_r($arTo, true))." =====================<br/>";
		return $mailer->manda();
	}else{
		$debug=false;
		if ($from){
			if (@strtolower(get_class($from))=='pessoa'){
				$fromName=$from->pess2_nom;
				$fromMail=$from->pess2_email;
			}elseif (is_array($from)){
				$fromName=$from['nome'];
				$fromMail=$from['email'];
			}else{
				$fromName=$from;
				$fromMail=$from;
			}
		}else{
			$fromName=$_configNomeEmailContato;
			$fromMail=$_configEmailContato;
		}
		$arTo=array();
		if (@strtolower(get_class($to))=='pessoa'){
			$toName=$to->pess2_nom;
			$toMail=$to->pess2_email;
			$arTo[]=$toMail;
		}elseif (is_array($to)){
			if (isset($to['nome'])){
				$toName=$to['nome'];
				$toMail=$to['email'];
				$arTo[]=$toMail;
			}else{
				if (isset($to[0]['nome'])){
					$toName=$to[0]['nome'];
					$toMail=$to[0]['email'];
				}else{
					$toName=$to[0];
					$toMail=$to[0];
				}
				$arTo=$toMail;
			}
		}else{
			if (strpos($to, ';')!==false){
				$arTo=explode(';', $to);
				$toName=$arTo[0];
				$toMail=$arTo[0];
			}else{
				$toName=$to;
				$toMail=$to;
				$arTo[]=$toMail;
			}
		}
		if ($debug){
			echo($fromName." - ".$fromMail."<br>");
			echo($toName." - ".$toMail."<br>");
		}
		$headers='';
		if ($rr==true) $headers="Disposition-Notification-To:".$fromMail."\r\n"."Return-Receipt-To:".$fromMail."\r\n";
		$headers.="Message-ID:<".time()."@".$_configDominio.">\r\n";
		$mail = new html_mime_mail($headers);
		if ($html){
			$mail->add_html($html, $text);
		}else{
			$mail->set_body($text);
		}
		$params=array();
		$mail->build_message($params);
		// Cria o objeto que efetivamente manda o email (criado acima);
		$mailer = new CONSTRUNET_MAILER(null, null, null, null, true);
		$mailer->mail = $mail;
		$mailer->returnpath = $fromMail;
		$mailer->de_nome = $fromName;
		$mailer->de_email = $fromMail;
		$mailer->assunto = $subj;
		$mailer->para_nome = $toName;
		$mailer->para_email = $toMail;
		$mailer->arraypara = $arTo;
		if ($bcc) {
			if (!is_array($bcc)) {
				$bcc=array($bcc);
			}else{
				if (isset($bcc['email']))$bcc=array($bcc);
			}
			$mailer->arraybcc=$bcc;
		}
		
		return $mailer->manda();
	}
}

function dmyToTimestamp($txt){
	$debug=false;
	if ($debug) echo $txt."------------------------------<br>";
	if (!$txt) return null;
	$txt=trim($txt);
	$dt=$txt;
	$tm="0:0:0";
	if (!(strpos($txt, " ")===false)){
		$dh=explode(" ", $txt);
		$dt=$dh[0];
		$tm=$dh[1];
	}
	if ($debug) echo "data: ".$dt."------------------------------<br>";
	if ($debug) echo "hora: ".$tm."------------------------------<br>";
	$dp=explode("/", $dt);
	$tm = explode(":",$tm);
	if ($debug){
		echo (sizeOf($dp))."--dp<br>";
		echo (sizeOf($tm))."--tm<br>";
	}
	if (sizeOf($dp)!=3 || !(sizeOf($tm)==3 | sizeOf($tm)==2)) return null;
	if ($debug) echo($dp[1]."/".$dp[0]."/".$dp[2]."<br>");
	if (sizeOf($tm)==2) $tm[]="0";
	if ($debug) echo($tm[0].":".$tm[1].":".$tm[2]."<br>");
	//$ts=mktime($tm[0], $tm[1], $tm[2], $dp[1], $dp[0], $dp[2]);
	$ts=adodb_mktime($tm[0], $tm[1], $tm[2], $dp[1], $dp[0], $dp[2]);
	if ($debug) echo $ts."++++++++++++++++++++++++++++++<br>";
	return $ts;
}

function parseTable($html){
	global $debug;
	//echo(htmlspecialchars($html)."<br>");
	$aTab=(object)null;
	//Pegamos a tag table
	preg_match_all("/<table[^>]*>/si", $html, $matches);
	$tagTable=@$matches[0][0];
	if (!$tagTable){
		if ($debug) echo ("Não veio cabecalho table<br>");
		$tagTable="<table border=0 cellpadding=0 cellspacing=0>";
	}
	$aTab->tagIni=$tagTable;
	$qtos = preg_match_all("/<\/table[^>]*>/i", $html, $matches);
	$endTable=@$matches[0][$qtos-1];
	if (!$endTable){
		$endTable="</table>";
	}
	$aTab->tagFim=$endTable;
	//Pegamos os TRs
	preg_match_all("/<tr[\/\!]*?.*?\/tr>/si", $html, $trs);
	$aTab->trs=$trs[0];
	return $aTab;
}

function parseTr($tag){
	global $debug;
	$oTr=(object)null;
	$oTr->tag=$tag;
	//pegamos o tag de abertura do tr
	$preg = "/<tr[^>]*>/six";
	preg_match_all($preg, $tag, $matches);
	$oTr->tagIni=$matches[0][0];
	if (!$oTr->tagIni) return null;
	//tag de fechamento
	$preg = "/<\/tr [^>]*>/six";
	preg_match_all($preg, $tag, $matches);
	$oTr->tagFim=$matches[0][0];
	if (!$oTr->tagFim) $oTr->tagFim="</tr>";
	//os TDs
	preg_match_all("/<td[\/\!]*?.*?\/td>/si", $tag, $retds);
	$oTr->tds=$retds[0];
	//attributes
	$mTag=str_replace(array("<", ">"), array("", " >"), $oTr->tagIni);
	//if ($debug) echo "vai parsear: ".htmlspecialchars($tag)."<br>";
	$caracs=array("align", "valign", "rowspan", "bgcolor", "style");
	$preg = "/(\\w+) \\s* ( = \\s* (?: (\"[^\"]*\" | '[^']*' | \\S*) ) )?/sx";
	preg_match_all($preg, $mTag, $regs);
	$names = $regs[1];
	$checks = $regs[2];
	$values = $regs[3];
	foreach ($names as $key=>$name){
		if (in_array(strtolower($name), $caracs)){
			//if ($debug) echo($name. " = ".$values[$key]." pego no foreach novo<br>");
			eval("\$oTr->$name=str_replace('\"', '', \$values[\$key]);");
		}
	}
	return $oTr;
}

function parseTd($tag){
	global $debug;
	$oTd=(object)null;
	$oTd->tag=$tag;
	$oTd->subtags=array();
	$oTd->colspan=1;
	$oTd->rowspan=1;
	//pegamos o tag de abertura do td
	$preg = "/<td[^>]*>/six";
	preg_match_all($preg, $tag, $matches);
	$oTd->tagIni=$matches[0][0];
	if (!$oTd->tagIni) return null;
	//tag de fechamento
	$preg = "/<\/td [^>]*>/six";
	preg_match_all($preg, $tag, $matches);
	$oTd->tagFim=$matches[0][0];
	if (!$oTd->tagFim) $oTd->tagFim="</td>";
	//conteúdo entre os tags de abertura e fechamento
	$preg = "|<td[^>]*>(.*)<\/td[^>]*>|six";
	preg_match_all($preg, $tag, $matches);
	$oTd->conteudo=$matches[1][0];
	//attributes
	$mTag=str_replace(array("<", ">"), array("", " >"), $oTd->tagIni);
	//if ($debug) echo "vai parsear: ".htmlspecialchars($tag)."<br>";
	$caracs=array("align", "valign", "width", "colspan", "bgcolor", "rowspan", "style");
	$preg = "/(\\w+) \\s* ( = \\s* (?: (\"[^\"]*\" | '[^']*' | \\S*) ) )?/sx";
	preg_match_all($preg, $mTag, $regs);
	$names = $regs[1];
	$checks = $regs[2];
	$values = $regs[3];
	foreach ($names as $key=>$name){
		if (in_array(strtolower($name), $caracs)){
			//if ($debug) echo($name. " = ".$values[$key]." pego no foreach novo<br>");
			eval("\$oTd->$name=str_replace('\"', '', \$values[\$key]);");
		}
	}
	return $oTd;
}

function getFuncStack(){
	if (function_exists("xdebug_get_function_stack")) {
		return xdebug_get_function_stack();
	} else {
		$stack=debug_backtrace();
		return limpaStack($stack);
	}
}
function limpaStack($stack){
	foreach($stack as $k=>$item){
		unset($stack[$k]['args']);
		unset($stack[$k]['object']);
		unset($stack[$k]['type']);
	}
	$stack=array_reverse($stack);
	return $stack;
}
function echoFuncStack(){
	$stack=cospeFuncStack();
	if (substr($stack, 0, 4)=='<br>') $stack=substr($stack, 4);
	echo '<div style="margin:2px;padding:2px;border:solid 1px lightgray;">'.$stack.'</div>';
}
function cospeFuncStack($stack=null, $emHtml=true){
	if (!$stack) $stack=getFuncStack();
	$txt='';
	$html='';
	if (sizeOf($stack)>0){
		$txt.="\r\nCall stack:";
		$html.='<br>Call stack:<UL>';
		foreach ($stack as $call){
			$txt.="\r\n-->".(@$call['class']?($call['class']."::"):"").(@$call['function']).(@$call['file']?" ".$call['file']:'').(@$call['line']?" (".$call['line'].")":'');
			$html.='<li>'.
				(@$call['class']?($call['class'].'::'):'').(@$call['function']).
				(@$call['file']?'&nbsp;<TT>'.$call['file'].'</TT>':'').
				(@$call['line']?'&nbsp;('.$call['line'].')':'').
				'</li>';
		}
		$html.='</UL>';
	}
	if ($emHtml) return $html; else return $txt;
}

function meuErrorHandler($errno, $errmsg, $filename, $linenum, $vars=null) {
	global $db, $_configEmailError, $_configEmailWebmaster, $_configEmailContato, $_configCustomErrorShow, $_configCustomErrorMail, $DEBUG_DB, $_configNomeSite, $_configSiteDevel, $_numNotices, $_sizeNotices;
	if ($errno == E_NOTICE && substr($errmsg, 0, 17) == "Undefined index: ") return;
	if ($errno == E_ERROR){
		if ($db){
			while ($db->transCnt>0){
				$db->RollbackTrans();
			}
		}
	}
	$replevel = error_reporting();
	if ($replevel==0) return;
	$errortype = array (1=>"Error", 2=>"Warning", 4=>"Parsing Error", 8=>"Notice", 16=>"Core Error", 32=>"Core Warning", 64=>"Compile Error", 128=>"Compile Warning", 256=>"User Error", 512=>"User Warning", 1024=>"User Notice", 2048=>"Strict", 4096=>"Recoverable", 8192=>"Deprecated", 16384=>"User deprecated", 30719=>"All");
	//definição do padrão de exibição e email
	if (@$_configSiteDevel){
		if (@!$_configCustomErrorShow) $_configCustomErrorShow = (E_ALL ^ E_DEPRECATED ^ E_WARNING);
		if (@!$_configCustomErrorMail) $_configCustomErrorMail = (E_ALL ^ E_NOTICE ^ E_DEPRECATED ^ E_WARNING);
	}else{
		if (@!$_configCustomErrorShow) $_configCustomErrorShow = (E_ALL ^ E_NOTICE ^ E_DEPRECATED ^ E_WARNING);
		if (@!$_configCustomErrorMail) $_configCustomErrorMail = (E_ALL ^ E_NOTICE ^ E_DEPRECATED ^ E_WARNING);
	}
	$cospe=(($errno & $_configCustomErrorShow) == $errno && $errno!=E_STRICT);
	if (false){
		echo (int)$_configCustomErrorShow."<br>";
		echo (($errno & $_configCustomErrorShow))."<br>";
		echo (( $errno!=E_STRICT))."<br>";
		echo (int)($cospe)."<br>";
		echo $errno." - ".$errmsg."<br>";
		exit;
	}
	$mandaMail=(($errno & $_configCustomErrorMail) == $errno);
	if (@$_sizeNotices && $_sizeNotices>1000) return;
	if ($cospe || $mandaMail) {
		if ($errno == E_NOTICE){
			if (@!$_numNotices)$_numNotices=1; else $_numNotices++;
			if ($_numNotices>20) return;
		}
		//$varTrace=wddx_serialize_value($vars,"Variables");
		$stack=getFuncStack();
		$errTxt="Erro: (".$errortype[$errno].") ".$errno." - ".$errmsg." em ".$filename." (".$linenum.")\r\n";
		$errHtml='<b>Erro:</b> ('.$errortype[$errno].') '.$errno.' - '.$errmsg.' em <b>'.$filename.' ('.$linenum.')</b>';
		$errTxt.="Path: ".$_SERVER['PHP_SELF'].(@$_SERVER['QUERY_STRING'] && $_SERVER['QUERY_STRING']!=''?'?'.$_SERVER['QUERY_STRING']:'')."\r\n";
		$errHtml.="Path: ".$_SERVER['PHP_SELF'].(@$_SERVER['QUERY_STRING'] && $_SERVER['QUERY_STRING']!=''?'?'.$_SERVER['QUERY_STRING']:'')."<br>";
		$varTrace='';
		$varTraceHtml='';
		if (@$vars){
			cospeVars($vars, $varTrace, $varTraceHtml, @$nivel);
			if ($varTrace!=''){
				$varTrace="\r\n\r\n----VARIÁVEIS----:".$varTrace;
				$varTraceHtml='<br><br>----VARIÁVEIS----:'.$varTraceHtml;
			}
		}
		$showTrace=true;
		$errTxt.=cospeFuncStack($stack, false);
		$errHtml.=cospeFuncStack($stack);
		if ($DEBUG_DB | $showTrace) $errHtml.=$varTraceHtml;
		if (function_exists('especificaErrorReport')){
			especificaErrorReport($errHtml, $errTxt);
		}
		$errHtml='<div style="Color:gray">'.$errHtml.'</div>';
		if ($cospe) {
			if (emCommandLine()){
				echo($errTxt);
			}else{
				echo $errHtml;
			}
		}
		//echo ((int)$cospe)." - ".$errno." - ".$errmsg."<br>";
		//exit;
		if (@!$_sizeNotices) $_sizeNotices=0;
		$_sizeNotices+=strlen($errTxt) + strlen($varTrace);
		if ($mandaMail) {
			$dest=$_configEmailError?$_configEmailError:($_configEmailWebmaster?$_configEmailWebmaster:$_configEmailContato);
			$iniFrom=ini_get('sendmail_from');
			$from=($iniFrom?$iniFrom:$_configEmailWebmaster);
			// save to the error log, and e-mail me if there is a critical user error
			//error_log($err, 3, "/usr/local/php4/error.log");
			//echo("Ia enviar email...<br>");
			$headers = 'From: '.$from . "\r\n" .
				'X-Mailer: PHP/' . phpversion();
			//$headers = null;
			$foi = mail($dest, '[Zica no site '.$_configNomeSite.']', $errTxt.'\r\n'.$varTrace, $headers);
		}
	}
}

function cospeStack(){
	$stack=array();
	$errHtml='';
	$stack=getFuncStack();
	if (sizeOf($stack)>0){
		$errHtml.='<br>Call stack:<UL>';
		foreach ($stack as $call){
			$errHtml.='<li>'.
				(@$call['class']?($call['class'].'::'):'').($call['function']).
				'&nbsp;<TT>'.$call['file'].'</TT>'.
				'&nbsp;('.$call['line'].')'.
				'</li>';
		}
		$errHtml=$errHtml.'</UL>';
	}
	echo $errHtml."<br>";
}
function cospeObj($obj){
	echo nl2br(str_replace(' ', '&nbsp;', print_r($obj, true))).'<br/>';
}
function cospeVars($vars, &$varTrace, &$varTraceHtml, $nivel=null, $limit=false){
	if (@!$nivel) $nivel=1;
	if ($nivel>10) return;
	$destaque='';
	for ($i=0; $i<$nivel; $i++){
		$destaque.="-=";
	}
	$destaque.="> ";
	$arignore=array('globals',
		'_env',
		'db',
		'_month_table_normal',
		'_month_table_leaf',
		'translate_from',
		'translate_to',
		'statuserroenvio',
		'ss_log_filename',
		'sapi',
		'_commandline',
		'_bypass',
		'_semprepend',
		'no_layout',
		'no_gz',
		'global_before',
		'ss_log_levels',
		'ss_log_level',
		'passareto',
		'passacache',
		'_ignoretimeout',
		'beanisolado',
		'construseek',
		'debug_stack',
		'debug_db',
		'debug_logacao',
		'no_cookie',
		'_edicaoempreview',
		'_useinnovastudioeditor',
		'_masterarquivo',
		'_masterarquivos',
		'_oracle',
		'_mssql',
		'_timeout',
		'roledefault',
		'roleadm',
		'mydire',
		'mypai',
		'myareas',
		'__s__mapasitearraylinear',
		'sqlcompleto',
		'_skipchecaipbrowser',
		'_descartamarcaquery',
		'__singletonmapa'
	);
	//print_r(error_get_last());exit;
	$arRGPC=array('_request','_get','_post','_cookie');
	$rgon=ini_get('register_globals');
	if ($rgon=='1') $arignore=array_merge($arignore, $arRGPC);
	$cont=0;
	foreach ($vars as $key=>$value){
		if (in_array(strtolower($key),$arignore)) continue;
		if (in_array($key, array_keys($_ENV))) continue;
		if (in_array($key, array_keys($_SERVER))) continue;
		if (substr($key,0,7)=='_config') continue;
		if (substr($key,0,3)=='db_') continue;
		if (substr($key,0,5)=='ADODB') continue;
		$cont++;
		if ($limit && $cont>$limit) {
			$varTrace.="\r\n(limitado em ".$limit." itens)";
			$varTraceHtml.='<br>(limitado em '.$limit.' itens)';
			break;
		}

		$forwarda=false;
		$fwLimit=3;
		if ($value===true){
			$txtValue="true";
			$htmlValue='true';
		}else if ($value===false){
			$txtValue="false";
			$htmlValue='false';
		}else if ($value==null){
			$txtValue="null";
			$htmlValue='null';
		}else if (is_numeric($value)){
			$txtValue=$value;
			$htmlValue=$value;
		}else if (is_array($value)){
			$txtValue="ARRAY (".sizeof($value)."):";
			$htmlValue='ARRAY('.sizeof($value).'):';
			if (count($value)==0){
				$txtValue.="vazio";
				$htmlValue.='vazio';
			}else{
				$forwarda=true;
				if (in_array(strtolower($key),$arRGPC)) $fwLimit=false;
				//echo $key.((int)$fwLimit).'======<br/>';
			}
		}else if (is_object($value)){
			//if ($key=='aqui') {echo '<br/><br/><br/>===== '.$key.((int)method_exists($value, 'varTrace'));exit;}
			//if ($key=='aqui') {echo '<br/><br/><br/>===== '.$key.get_class($value);exit;}
			if (method_exists($value, 'varTrace')) {
				$txtValue=$value->varTrace();
			} else {
				$txtValue=print_r($value, true);
			}
			if (method_exists($value, 'varTraceHtml')) {
				$htmlValue=$value->varTraceHtml();
			} else {
				$htmlValue=nl2br(print_r($value, true));
			}
		}else{
			$txtValue="\"".$value."\"";
			$htmlValue='&quot;'.$value.'&quot;';
		}
		$varTrace.="\r\n".$destaque.$key."=".$txtValue;
		$varTraceHtml.='<br>'.$destaque.$key.'='.$htmlValue;
		if ($forwarda) cospeVars($value, $varTrace, $varTraceHtml, $nivel+1, $fwLimit);
		if (strlen($varTrace)>4000) {
			$varTrace.="\r\n\r\n---------------------- EXCEDE 4.000 CARACTERES -------------------------";
			$varTraceHtml.='<br><br>---------------------- EXCEDE 4.000 CARACTERES -------------------------';
			return;
			break;
		}
	}
}

function soNumeros($txt){
	return getSoNumeros($txt);
}

function getSoNumeros($txt){
	return preg_replace('/\D/i', '', $txt);
}

function doCSV_while($arrLinhas, $nomeArquivo, $sep=','){
	global $_configDominio;
	$path = realpath($_SERVER["DOCUMENT_ROOT"]."/../userfiles/");
	if(!file_exists($path."/export/")){
		mkdir($path."/export/",0777);
	}
	$pathBase = realpath($_SERVER["DOCUMENT_ROOT"]."/../userfiles/export/");
	if ($handle = opendir($pathBase)) {
	    while (false !== ($file = readdir($handle))) {
	        if (!is_dir($file)){
				if ($modif=filemtime($pathBase.'/'.$file)){
					if ($modif<time()-60*60*24*10){
						unlink($pathBase.'/'.$file);
					}
				}
			}
	    }
		closedir($handle);
	}
	$arqCsv = $pathBase."/".$nomeArquivo;
	$fcsv = fopen ($arqCsv, "wb+");
	if (is_array($arrLinhas)){
		foreach($arrLinhas as $umaLinha){
			if (is_array($umaLinha)){
				fputcsv($fcsv, $umaLinha, $sep);
			}else{
				fputs($fcsv, $umaLinha."\n");
			}
		}
	}else{
		fputs($fcsv, $arrLinhas);
	}
	fclose ($fcsv);
	//echo $arqCsv."---------<br>";
	return '/userfiles/export/'.$nomeArquivo;
}

function getNumeroVirgula($val){
	$posUltVirg=strrpos($val, ",");
	//echo($posUltVirg."<br>");
	$posUltPto=strrpos($val, ".");
	if ($posUltVirg===false){
		if ($posUltPto===false) {
			return $val;
		}else{
			$posPrimPto=strpos($val, ".");
			if ($posPrimPto==$posUltPto) return $val; else return str_replace(".", "", $val);
		}
	}
	//echo($posUltPto."<br>");
	if ($posUltPto===false) return str_replace(",", ".", $val);
	if ($posUltVirg>$posUltPto){
		$val=str_replace(".", "", $val);
		$val=str_replace(",", ".", $val);
	}else{
		$val=str_replace(",", "", $val);
	}
	return $val;
}

function formataNumero($val, $numcasas){
	if (!is_numeric($val)) return $val;
	if (!is_numeric($numcasas)) return $val;
	if ($numcasas<0) return $val;
	$caseador=pow(10, $numcasas);
	$val=round($val * $caseador) / $caseador;
	$str=$val;
	$re = '/\./g';
	$fm = str_replace(".", ",", $str);
	$pos = strpos($fm, ",");
	$zeros=str_repeat("0", $numcasas);
	if ($pos===false){
		$fm.=($numcasas==0?'':',').$zeros;
		$pos = strpos($fm, ",");
	}else{
		$fm.=$zeros;
		$fm = substr($fm, 0, $pos+$numcasas+1);
	}
	if ($pos===false) $pos=strlen($fm);
	//echo((int)$pos." --<br>");
	for ($i=$pos-3; $i>0; $i-=3){
		$fm=substr($fm, 0, $i).'.'.substr($fm, $i, strlen($fm));
	}
	return $fm;
}

function validaNumeroInsertUpdate($valor){
	if(is_null($valor) | !isset($valor)) return null;
	$posiPonto = strrpos($valor, '.');
	$posiVirg = strrpos($valor, ',');
	if($posiVirg>$posiPonto){
		$valorBruto = str_replace(".","",$valor);
		$valortratado = $item->ofer17_preco = str_replace(",",".",$valorBruto);
	} else if($posiVirg === $posiPonto){
		$valortratado = $valor;
	} else {
		$valortratado = str_replace(",",".",$valor);
	}
	return $valortratado;
}


function checkRestaurarTemplateMailcenter($onde){
	if ($layo=$onde->getLAYOUT()){
		if (getPathTemplate($layo->layo3_txt)){
			return true;
		}
	}
	return false;
}

function getPathTemplate($layoTxt){
	$urlAdm="/statmkt/admsite/admmail/admtemplatesmail/";
	$dirAdm=$_SERVER["DOCUMENT_ROOT"].$urlAdm;
	if ($handle = opendir($dirAdm)) {
		while (false !== ($file = readdir($handle))) {
			if ($file != "." && $file != ".." && $file!="index.php") {
				if ($file==$layoTxt.".php"){
					$oPath=&FACTORY::PATH();
					if ($oPath->getByFullPath($urlAdm.$file)){
						closedir($handle);
						return $oPath;
					}
				}
			}
		}
		closedir($handle);
	}
	return null;
}

function getArrayFromList($lista, $chave=null){
	$arr=array();
	while($item=$lista->getNextItem()){
		if ($chave){
			$arr[$item->$chave]=$item;
		}else{
			$arr[]=$item;
		}
	}
	return $arr;
}

function getCSVPessoas($arrPess){
	$csv="pess2_cod;nome;email;endereço;bairro;cidade;estado;país;telefone;ocupação\r\n";
	set_time_limit(0);
	$arEsta=array();
	$arPais=array();
	$arOcup=array();
	foreach($arrPess as $pessCod){
		$pess=&FACTORY::PESSOA();
		if (!$pess->loadByPk($pessCod)) continue;
		if ($pess->ocup2_cod){
			if (isset($arOcup[$pess->ocup2_cod])){
				$ocup=$arOcup[$pess->ocup2_cod];
			}else{
				$ocup = $pess->getOCUPACAO();
				$arOcup[$pess->ocup2_cod]=$ocup;
			}
		}
		if ($ende = $pess->getENDERECO()){
			if($ende->esta2_cod) {
				if (isset($arEsta[$ende->esta2_cod])){
					$uf=$arEsta[$ende->esta2_cod];
				}else{
					$uf = $ende->getESTADO_DO_PAIS();
					$arEsta[$ende->esta2_cod]=$uf;
				}
				if($uf && $uf->pais2_cod) {
					if (isset($arPais[$uf->pais2_cod])){
						$pais=$arPais[$uf->pais2_cod];
					}else{
						$pais = $uf->getPAIS();
						$arPais[$uf->pais2_cod]=$pais;
					}
				}
			}
		}
		if ($tels=$pess->getTELEFONE_LIST()){
			$sep="";
			while($tel=$tels->getNextItem()){
				$txtTels.=$sep.$tel->formata();
				$sep="/";
			}
		}
		$csv.=@$pess->pess2_cod.';"'.@$pess->pess2_nom.'";"'.@$pess->pess2_email.'";"'.@$ende->ende2_end1.'";"'.@$ende->ende2_end2.'";"'.@$ende->ende2_cida.'";"'.(@$uf->esta2_abr?$uf->esta2_abr:@$uf->esta2_nom).'";"'.@$pais->pais2_nom.'";"'.@$txtTels.'";"'.@$ocup->ocup2_nom."\"\r\n";
	}
	return $csv;
}

function getLinkBaseHref(){
	global $_linkBaseHref, $HTTP_SERVER_VARS;
	if ($_linkBaseHref) return $_linkBaseHref;
	$servidor=$_SERVER['HTTP_X_FORWARDED_HOST']?$_SERVER['HTTP_X_FORWARDED_HOST']:$_SERVER["HTTP_HOST"];
	$linkBase="http://".trim((@$_configMailcenterBasehref ? $_configMailcenterBasehref : $servidor));
	if (substr($linkBase, -1)=="/") $linkBase=substr($linkBase, 0, -1);
	$GLOBALS["_linkBaseHref"]=$linkBase;
	return $linkBase;
}

function checaShowIndice($cont, $pag, $porPag, $bool=true){
	$naPag=ceil($cont/$porPag);
	if ($bool==true){
		if ($naPag>$pag) return null;
		return ($naPag==$pag);
	}
	if ($naPag<$pag){
		return -1;
	}else if ($naPag>$pag){
		return 1;
	}else{
		return 0;
	}
}

function getIndice($arr, $campo, $porPag, $pag, $qs, $indiceShort=true, $indiceNum=false){
	$PHP_SELF=$_SERVER["PHP_SELF"];
	// se indiceShort=false o formato é: com indiceNum=true: 1 | 2 | 3;          com indiceNum=false: Alva-Carl | Carl-Deni | Deni-Fabi
	// se indiceShort=true  o formato é: com indiceNum=true: anterior | proxima; com indiceNum=false: primeira | anterior | próxima | última
	$contPag=0;
	$ndx='';
	$sep='';
	$ini='';
	if (is_array($arr)){
		$total=sizeOf($arr);
	}else if (get_class($arr) && method_exists($arr, 'getQuantos')){
		$total=$arr->getQuantos();
	}else{
		return '';
	}
	$numPags=ceil($total/$porPag);
	if ($numPags==1) return '';
	if ($indiceShort){
		$sep='&nbsp;';
		$offset=ceil($numPags/5);
		if ($offset<3)$offset=3;
		if ($offset>10)$offset=10;
		$txt="";
		for ($i=$pag - $offset; $i<=$pag + $offset; $i++){
			if ($i>0 && $i<=$numPags){
				if ($i==$pag){
					$txt.=" $i ";
				}else{
					$txt.=' <a href="'.montaHref($PHP_SELF).'&pag='.($i).$qs.'">'.$i.'</a> ';
				}
			}
		}
		$txt='<b>'.$txt.'</b>';
		if ($pag-$offset>1) $txt="...".$txt;
		if ($pag+$offset<$numPags) $txt.="...";
		if ($indiceNum){
			if ($pag==1) $ndx.='<img src="/images/vai_pag_ant.gif" align="absmiddle" alt="Anterior" border="0">'; else $ndx.='<a href="'.montaHref($PHP_SELF).'&pag='.($pag-1).$qs.'"><img src="/images/vai_pag_ant.gif" align="absmiddle" alt="Anterior" border="0"></a>';
			$ndx.=$sep;
			//$ndx.='<b>'.$pag.' de '.$numPags.'</b>';
			$ndx.=$txt;
			$ndx.=$sep;
			if ($pag==$numPags) $ndx.='<img src="/images/vai_pag_prox.gif" align="absmiddle" alt="Próxima" border="0">'; else $ndx.='<a href="'.montaHref($PHP_SELF).'&&pag='.($pag+1).$qs.'"><img src="/images/vai_pag_prox.gif" align="absmiddle" alt="Próxima" border="0"></a>';
		}else{
			if ($pag==1) $ndx.='<img src="/images/vai_pag_prim.gif" align="absmiddle" alt="Primeira" border="0">'; else $ndx.='<a href="'.montaHref($PHP_SELF).'&pag=1'.$qs.'"><img src="/images/vai_pag_prim.gif" align="absmiddle" alt="Primeira" border="0"></a>';
			$ndx.=$sep;
			if ($pag==1) $ndx.='<img src="/images/vai_pag_ant.gif" align="absmiddle" alt="Anterior" border="0">'; else $ndx.='<a href="'.montaHref($PHP_SELF).'&pag='.($pag-1).$qs.'"><img src="/images/vai_pag_ant.gif" align="absmiddle" alt="Anterior" border="0"></a>';
			$ndx.=$sep;
			//$ndx.='<b>'.$pag.' de '.$numPags.'</b>';
			$ndx.=$txt;
			$ndx.=$sep;
			if ($pag==$numPags) $ndx.='<img src="/images/vai_pag_prox.gif" align="absmiddle" alt="Próxima" border="0">'; else $ndx.='<a href="'.montaHref($PHP_SELF).'&pag='.($pag+1).$qs.'"><img src="/images/vai_pag_prox.gif" align="absmiddle" alt="Próxima" border="0"></a>';
			$ndx.=$sep;
			if ($pag==$numPags) $ndx.='<img src="/images/vai_pag_ult.gif" align="absmiddle" alt="Última" border="0">'; else $ndx.='<a href="'.montaHref($PHP_SELF).'&pag='.$numPags.$qs.'"><img src="/images/vai_pag_ult.gif" align="absmiddle" alt="Última" border="0"></a>';
		}
		return $ndx;
	}
	$cont=0;
	if (is_array($arr)){
		foreach ($arr as $item){
			$cont++;
			if ($cont%$porPag==1){
				$nome=$item->$campo;
				if (!$indiceNum) $ini=getTextoIndice($nome, $campo).'-'; else $ini='';
				//echo($ini." ini<br>");
				$fim=null;
			}
			if ($cont%$porPag==0 || $cont==$total){
				$nome=$item->$campo;
				//echo($nome."<br>");
				$fim=getTextoIndice($nome, $campo);
				//echo($fim." fim<br>");
			}
			if ($fim) {
				$contPag++;
				$ndx.=$sep;
				if ($indiceNum) $naco=$contPag; else $naco=$ini.$fim;
				$sep=' |&nbsp;';
				if ($contPag==$pag){
					$ndx.='<b>'.$naco.'</b>';
				}else{
					$ndx.='<a href="'.montaHref($PHP_SELF).'&pag='.$contPag.$qs.'">'.$naco.'</a>';
				}
			}
		}
	}else{
		while ($item=$arr->getNextItem()){
			$cont++;
			if ($cont%$porPag==1){
				$nome=$item->$campo;
				if (!$indiceNum) $ini=getTextoIndice($nome, $campo).'-'; else $ini='';
				//echo($ini." ini<br>");
				$fim=null;
			}
			if ($cont%$porPag==0 || $cont==$total){
				$nome=$item->$campo;
				//echo($nome."<br>");
				$fim=getTextoIndice($nome, $campo);
				//echo($fim." fim<br>");
			}
			if ($fim) {
				$contPag++;
				$ndx.=$sep;
				if ($indiceNum) $naco=$contPag; else $naco=$ini.$fim;
				$sep=' |&nbsp;';
				if ($contPag==$pag){
					$ndx.='<b>'.$naco.'</b>';
				}else{
					$ndx.='<a href="'.montaHref($PHP_SELF).'&pag='.$contPag.$qs.'">'.$naco.'</a>';
				}
			}
		}
	}
	return $ndx;
}

function getTextoIndice($val, $campo){
	if (is_numeric($val) && strpos($campo, "dat")!==false){
		return showData($val, 1, 2, false, false);
	}else{
		return substr(str_replace(' ', '', $val), 0, 3);
	}
}

function getRoleAdm(){
	global $roleAdm;
	if ($roleAdm) return $roleAdm;
	$roleAdm = &FACTORY::ROLE();
	$roleAdm->getByNom("Adm");
	return $roleAdm;
}

function getQsBean($obj){
	if (isset($obj->bean2_cod) && isset($obj->arpa3_cod) && isset($obj->bear3_ord)){
		return 'bear='.$obj->bean2_cod.'|'.$obj->arpa3_cod.'|'.$obj->bear3_ord;
	}else{
		return '';
	}
}

function getHiddenBean($obj){
	if (isset($obj->bean2_cod) && isset($obj->arpa3_cod) && isset($obj->bear3_ord)){
		return "<input type=\"hidden\" name=\"bear\" value=\"".$obj->bean2_cod.'|'.$obj->arpa3_cod.'|'.$obj->bear3_ord ."\">";
	}else{
		return '';
	}
}

function cospeBeanArea($bean2_cod, $arpa3_cod, $bear3_ord, $adm=false){
	global $acao, $modo_adm_docu;
	$bear=&FACTORY::BEAN_DA_AREA();
	//echo($arpa3_cod." - ".$bean2_cod." - ".$bear3_ord."<br>");
	if ($bear->getByCodigo_da_areaAndCodigo_do_beanAndOrdem_do_bean_na_area($arpa3_cod, $bean2_cod, $bear3_ord)) {
		if ($acao){
			$isadm=$acao->pessoa->checkPermEdit();
			$isadm=$isadm & isset($modo_adm_docu);
		}else{
			$isadm=false;
		}
		cospeBear($bear, $isadm);
	}else{
		echo("não carregou<br>");
	}
}

function cospeBeae($beae, $adm=false){
	$oBean=$beae->getBEAN();
	$vcbeList=$beae->getVALOR_DA_CARACTERISTICA_DO_BEAN_NA_AREA_EM_EDICAO_LIST();
	$caracs=array();
	while ($item=$vcbeList->getNextItem()){
		$cabe=$item->getCARACTERISTICA_DO_BEAN();
		$caracs[$cabe->cabe2_txt]=$item->vcbe2_val;
	}
	$caracs['bear3_ord']=$beae->beae3_ord;
	$caracs['arpa3_cod']=$beae->arpe3_cod;
	$caracs['bean2_cod']=$beae->bean2_cod;
	if ($adm) echo(cospeInitAdmBeanPE($beae, $oBean));
	$umBean=getBean($oBean->bean2_txt, $caracs);
	if ($adm) echo(cospeEndAdmBeanPE());
}

function cospeBear($bear, $adm=false){
	global $modo_adm_docu, $db, $sqlCompleto;
	$oBean=$bear->getBEAN();
	$sql="select cabe2_txt, vcba2_val
	from tb2_cabe cb, tb2_vcba vc
	where cb.cabe2_cod=vc.cabe2_cod
	and bear3_cod=?
	";
	if (!$rs=$db->ExecuteBind($sql, array(array('integer', $bear->bear3_cod)))){
		$db->_raiseError(__FILE__,__LINE__,$sqlCompleto);
	}else{
		$caracs=array();
		while(!$rs->EOF){
			$caracs[$rs->fields(0)]=$rs->fields(1);
			$rs->MoveNext();
		}
	}
	/*
	$vcbaList=$bear->getVALOR_DA_CARACTERISTICA_DO_BEAN_NA_AREA_LIST();
	$caracs=array();
	while ($vcba=$vcbaList->getNextItem()){
		$cabe=$vcba->getCARACTERISTICA_DO_BEAN();
		$caracs[$cabe->cabe2_txt]=$vcba->vcba2_val;
	}
	*/
	$caracs['bear3_ord']=$bear->bear3_ord;
	$caracs['arpa3_cod']=$bear->arpa3_cod;
	$caracs['bean2_cod']=$bear->bean2_cod;
	if ($adm && $modo_adm_docu) echo(cospeInitAdmBean($bear, $oBean));
	$umBean=getBean($oBean->bean2_txt, $caracs);
	if ($adm && $modo_adm_docu) echo(cospeEndAdmBean());
}

function cospeInitAdmBeanPE($beae, $bean){
	global $_configAdmDocuStyleBorder, $_configAdmDocuNumBean;
	$meuObj = "tbadmbean".$beae->arpe3_cod."|".$beae->bean2_cod."|".$beae->beae3_ord;
	$arpeCod=$beae->arpe3_cod;
	$arpeOrd=($beae->beae3_ord?$beae->beae3_ord:"0");
	getMenuAdmBeanPE();
	// -----------------tabela mestra ----------------------------------
	$s = "
<TABLE width=\"100%\" cellpadding=\"0\" border=\"0\" cellspacing=\"0\" id=\"$meuObj\" class=\"tbAdmDocuNao\">
<TR class=\"admdocu_edit_class\">
	<TD $_configAdmDocuStyleBorder>";
	$s .= "
<!-----------------tabela do menu do documento, versao hiermenu-------------------->
<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">
<tr>
  <td width=\"100%\" height=\"10\">
  <a title=\"&gt; Módulo:$bean->bean2_nom $_configAdmDocuNumBean\" href=\"javascript: var notvoid=0;\" style=\"font-family: Arial; font-size: 8pt;color:#cc0000\" onMouseOver=\"jsArpaAtual = ".$arpeCod."; jsOrdAtual = ".$arpeOrd."; jsBeanAtual = ".$beae->bean2_cod."; if (HM_DOM) {document.getElementById('$meuObj').className = 'tbAdmDocuSim';}\" onMouseOut=\"jsBeanAtual = ".$beae->bean2_cod."; if (HM_DOM) {document.getElementById('$meuObj').className = 'tbAdmDocuNao';};HM_f_PopDown('elMenu".$_configAdmDocuNumBean."')\" onClick=\" HM_f_PopUp('elMenu".$_configAdmDocuNumBean."',event)\"><img align=\"absmiddle\" src=\"/images/adm/ico_bean.gif\" alt=\"\" width=16 height=14 border=0> Módulo: '$bean->bean2_nom'". (($arpeOrd!="null")?" ordem: ".$arpeOrd:"")."</a>
  </td>
</tr>
</table>
";
	$s .= "
	</TD>
</TR>
<TR>
	<TD>";
	return $s;
}

function cospeEndAdmBeanPE(){
	$s = "
		</TD>
	</TR>
</TABLE>
";
	return $s;
}

function cospeInitAdmBean($bear, $bean){
	global $_configAdmDocuStyleBorder, $_configAdmDocuNumBean, $_utf8;
	$meuObj = "tbadmbean".$bear->arpa3_cod."|".$bear->bean2_cod."|".$bear->bear3_ord;
	$arpaCod=$bear->arpa3_cod;
	$arpaOrd=($bear->bear3_ord?$bear->bear3_ord:"0");
	$tit="Módulo: '$bean->bean2_nom'";
	if (@$_utf8)$tit=utf8_encode($tit);
	getMenuAdmBean();
	// -----------------tabela mestra ----------------------------------
	$s = "
<TABLE width=\"100%\" cellpadding=\"0\" border=\"0\" cellspacing=\"0\" id=\"$meuObj\" class=\"tbAdmDocuNao\">
<TR class=\"admdocu_edit_class\">
	<TD $_configAdmDocuStyleBorder>";
	$s .= "
<!-----------------tabela do menu do documento, versao hiermenu-------------------->
<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">
<tr>
  <td width=\"100%\" height=\"10\">
  <a title=\"&gt; ".$tit." $_configAdmDocuNumBean\" href=\"javascript: var notvoid=0;\" style=\"font-family: Arial; font-size: 8pt;color:#cc0000\" onMouseOver=\"jsArpaAtual = ".$arpaCod."; jsOrdAtual = ".$arpaOrd."; jsBeanAtual = ".$bear->bean2_cod."; if (HM_DOM) {document.getElementById('$meuObj').className = 'tbAdmDocuSim';}\" onMouseOut=\"jsBeanAtual = ".$bear->bean2_cod."; if (HM_DOM) {document.getElementById('$meuObj').className = 'tbAdmDocuNao';};HM_f_PopDown('elMenu".$_configAdmDocuNumBean."')\" onClick=\" HM_f_PopUp('elMenu".$_configAdmDocuNumBean."',event)\"><img align=\"absmiddle\" src=\"/images/adm/ico_bean.gif\" alt=\"\" width=16 height=14 border=0> ".$tit. (($arpaOrd!="null")?" ordem: ".$arpaOrd:"")."</a>
  </td>
</tr>
</table>
";
	$s .= "
	</TD>
</TR>
<TR>
	<TD>";
	return $s;
}

function cospeEndAdmBean(){
	$s = "
		</TD>
	</TR>
</TABLE>
";
	return $s;
}

function cospeJSfocaCampoPorId(){
	global $_jaCuspiuFocaCampoPorId;
	if ($_jaCuspiuFocaCampoPorId==true) return;
	$_jaCuspiuFocaCampoPorId=true;
?>
function focaCampoPorId(id, seleciona){
	var campo=document.getElementById(id);
	if (null!=campo){
		campo.focus();
		if (seleciona==true) campo.select();
		//alert('foquei');
	}
}
<?
}

function focaCampoPorId($id, $seleciona=true){
?>
<script language="JavaScript1.2">
<?=cospeJSfocaCampoPorId()?>
focaCampoPorId('<?=$id?>', <?=($seleciona?'true':'false')?>);
</script>
<?
}

function checkIpsLiberados(){
	global $_configIpsLiberados, $acao;
	if (!@$_configIpsLiberados) return false;
	$ip=$acao->myIP->ipip3_ip;
	foreach($_configIpsLiberados as $ipLib){
		if (substr_count($ipLib, ".")==3 && is_numeric(str_replace(".", "", $ipLib))){//é ip - tem de ter 3 pontos
			//echo($ip." - ".$ipLib."<br>");
			if (strpos($ip, $ipLib)===0) return true; // permite que se defina uma sub rede
		}else{//é nome
			$oIp=gethostbyname($ipLib);
			//echo($ip." - ".$oIp." - ".$ipLib." - ".((int)($oIp==$ip))."<br>");
			if ($oIp==$ip) return true;
		}
	}
	return false;
}

function getRodapeDonoBasico(){
	global $_configCodPesjDono;
	if($_configCodPesjDono){
		$pesjDono = &FACTORY::PESSOA_JURIDICA();
		$pesjDono->loadByPk($_configCodPesjDono);
		$ende = $pesjDono->getENDERECO();
		$tepeList = $pesjDono->getTELEFONE_LIST();
	?>
	<strong><?= $pesjDono->pesj2_nom ?></strong><br>
	<div style="font-size:10px;">
<?
		if ($ende){
?>
	<?= $ende->ende2_end1 ?> <?= $ende->ende2_end2 ?> - <?= $ende->ende2_cep ?> - <?= $ende->ende2_cida ?> <?= $ende->ende2_esta?" - ".$ende->ende2_esta:"" ?><br />
<?
		}
		if($tepeList->getQuantos()>0){
			while($tepe = $tepeList->getNextItem()){
		?>
		<?= $tepe->tepe2_desc?$tepe->tepe2_desc." ":"" ?><?= $tepe->tepe2_ddd?$tepe->tepe2_ddd." - " :"" ?><?= $tepe->tepe2_num ?>
		<?
			}
	?>
	<br />E-mail <a href="mailto:<?= $pesjDono->pesj2_email ?>"><?= $pesjDono->pesj2_email ?></a>
	<?
		}
	?>
	</div>
	<?
	}
}

function getRodapeDonoFbidden(){
	global $_configCodPesjDono;
	$pesjDono = &FACTORY::PESSOA_JURIDICA();
	if($pesjDono->loadByPk($_configCodPesjDono)) {
		$ende = $pesjDono->getENDERECO();
		$esta = $ende->getESTADO_DO_PAIS();
		$tepeList = $pesjDono->getTELEFONE_LIST();
		$cep1 = substr($ende->ende2_cep,0,5);
		$cep2 = substr($ende->ende2_cep,5,3);
		$cep = $cep1."-".$cep2;
		?>
		<span class="tituloRodape"><?= $pesjDono->pesj2_nom ?></span><br>
		<div style="font-size:10px;padding-top:5px;">
		<?= $ende->ende2_end1 ?> - CEP: <?= $cep ?> - <?= $ende->ende2_end2 ?> - <?= $ende->ende2_cida ?> <?= $esta->esta2_abr?" / ".$esta->esta2_abr:"" ?><br />
		<?
		if($tepeList->getQuantos()>0){
			while($tepe = $tepeList->getNextItem()){
			?>
			<?= $tepe->tepe2_desc?$tepe->tepe2_desc.": ":"" ?><?= $tepe->tepe2_ddd?$tepe->tepe2_ddd." - " :"" ?><?= $tepe->tepe2_num ?><br>
			<?
			}
		}
		?>
		</div>
<?
	} else {
		return "&copy; ".date("Y");
	}
}

function checkCookieEmEdicao($path){
	return (@$_COOKIE['path'.$path->path3_cod]=='true');
}

function checkCookieViewEdicao($path){
	return (@$_COOKIE['path'.$path->path3_cod]=='view');
}

function cospeAreaIndep($codTxt, $pathBase=null, $txtSeVazio=null){
	global $aqui;
	$vars=array("areaIndep"=>$codTxt);
	if ($pathBase) $vars['path']=$pathBase;
	$oblen=ob_get_length();
	$conteudo = getBean("conteudo", $vars);
	$oblen2=ob_get_length();
	if ($oblen==$oblen2 && empty($txtSeVazio)==false) echo $txtSeVazio;
}

function cospeMensagemCadastro(){
	global $acao;
	if ($redir=$_REQUEST['redir']){
		if (function_exists('especificaCospeMensagemRedirCadastro')){
			especificaCospeMensagemRedirCadastro($redir);
		}else{
			cospeMensagemRedirCadastro($redir);
		}
	}
}

function cospeMensagemRedirCadastro($redir){
	if (!$redir) return;
	switch ($redir){
		default:
			return;
	}
}
function getDocRoot($letterOnWindows=false){
	$_DOCROOT = getcwd();
	$dirs = explode(DIRECTORY_SEPARATOR, $_DOCROOT);
	$fullpath = "";
	if(preg_match("/[A-Z]:/i", $dirs[0])) {
		$fullpath=$dirs[0];
		array_shift($dirs);
	}
	if (!$letterOnWindows) $fullpath = "";
	if (DIRECTORY_SEPARATOR=='/') array_shift($dirs);
	$achouBaseSite=false;
	foreach($dirs as $umDir) {
		$fullpath .= DIRECTORY_SEPARATOR . $umDir;
		if($achouBaseSite==false && file_exists($fullpath."/site/construnet/codebase")) $achouBaseSite=true;
		if($achouBaseSite==false && file_exists($fullpath."/site/cnet2/codebase")) $achouBaseSite=true;
		if($achouBaseSite==false && file_exists($fullpath."/site/cnetutf8/codebase")) $achouBaseSite=true;
		if ($achouBaseSite==true && substr($umDir, 0, 7)=='docroot'){
			//echo($fullpath." -------------------<br>");
			break;
		}
	}
	$pathBase=$fullpath;
	return $pathBase;
}
function getPathLayout(){
	global $_configSiteRoot;
	if (@$_configSiteRoot) return $_configSiteRoot.'/layout/';
	$pathBase=realpath($_SERVER["DOCUMENT_ROOT"]."/../layout/");
	return $pathBase;
}
function getSiteRoot(){
	global $_configSiteRoot;
	if (@$_configSiteRoot) return $_configSiteRoot;
	if (emCommandLine()){
		if (!defined('DS')) define("DS", DIRECTORY_SEPARATOR);
		$_DOCROOT = realpath('.');
		$dirs = explode(DIRECTORY_SEPARATOR, $_DOCROOT);
		if(preg_match("/[A-Z]:/i", $dirs[0])) array_shift($dirs);
		$fullpath = "";
		foreach($dirs as $umDir) {
			$fullpath = $fullpath . "/" . $umDir;
			if(file_exists($fullpath."/site/construnet/codebase")) break;
		}
		$pathBase=realpath($fullpath).'/site/construnet/';
	}else{
		$pathBase=realpath($_SERVER["DOCUMENT_ROOT"]."/../");
	}
	return $pathBase;
}

function getPathCache(){
	$pathBase=getSiteRoot();
	$pathCache=$pathBase.DIRECTORY_SEPARATOR."cacheroot".DIRECTORY_SEPARATOR;
	if (!file_exists($pathCache)){
		mkdir($pathCache, 0777);
	}
	if (file_exists($pathCache)) return $pathCache; else return null;
}

function getLoadSmtp(){
	if (isWindows()) return null;
	global $_configPaginaUptime, $_configNumProcessors;
	if ($_configPaginaUptime){
		$lines = file ($_configPaginaUptime);
		$resp=null;
		foreach($lines as $line){
			if (strpos($line, "recarrega")) continue;
			if (strpos($line, "load average")) {
				$resp = $line;
				break;
			}
		}
		if (!$resp) return null;
	}else{
		$resp=exec('uptime');
	}
	if (@!$_configNumProcessors) $_configNumProcessors=1;
	$vals=explode(",", $resp);
	if (!isset($vals[3])) return null;
	$vals2=explode(":", $vals[3]);
	if (!isset($vals2[1])) return null;
	$resp=$vals2[1];
	if (is_numeric($resp)) return $resp/$_configNumProcessors; else return null;
}

// O array de types pode passar os seguintes valores:
// "d" para datas (as datas podem vir em timestamp ou texto)
// "n" para números
// todo o resto vai como texto (com aspas)
function addLinhaCsv_(&$csv, $arr, $types=null){
	$linha='';
	$sep='';
	foreach($arr as $key=>$val){
		if (!$val) $val='';
		$linha.=$sep;
		if (@$types && @$types[$key]) $tipo=$types[$key]; else $tipo="s";
		switch($tipo){
			case 'd':
				if (is_numeric($val)){
					$linha.='"'.showData($val, 1, 2).'"';
				}else{
					$linha.='"'.$val.'"';
				}
				break;
			case 'n':
				$linha.=str_replace(".",",",$val);
				break;
			default:
				$linha.='"'.str_replace('"', '""', $val).'"';
		}
		$sep=";";
	}
	$csv.=$linha."\r\n";
}
function addLinhaCsv(&$csv, $arr, $types=null){
        $linha=getLinhaCsv($arr, $types);
        $csv.=$linha."\r\n";
}
function getLinhaCsv($arr, $types=null){
        $linha='';
        $sep='';
        foreach($arr as $key=>$val){
                if (!isset($val)) $val='';
                $linha.=$sep;
                if (@$types && @$types[$key]) $tipo=$types[$key]; else $tipo="s";
                switch($tipo){
                        case 'd':
                                if (is_numeric($val)){
                                        $linha.='"'.showData($val, 1, 2).'"';
                                }else{
                                        $linha.='"'.$val.'"';
                                }
                                break;
                        case 'n':
                                $linha.=str_replace(".",",",$val);
                                break;
                        default:
                                $linha.='"'.str_replace('"', '""', $val).'"';
                }
                $sep=";";
        }
        return $linha;
}
function cospeJSFuncoesCookies(){
	global $_funcoesJSCookieCuspidas;
	if (@$_funcoesJSCookieCuspidas==true) return;
?>
<script language="JavaScript" type="text/javascript">
function jsSetCookie (cookieName, cookieValue, expires, path, domain, secure) {
  document.cookie =
    escape(cookieName) + '=' + escape(cookieValue)
    + (expires ? '; EXPIRES=' + expires.toGMTString() : '')
    + (path ? '; PATH=' + path : '')
    + (domain ? '; DOMAIN=' + domain : '')
    + (secure ? '; SECURE' : '');
}
function readCookieVal(nome){
	var nameEQ = nome + "=";
	//alert(document.cookie);
	var ca = document.cookie.split(';');
	for(var i=0;i < ca.length;i++) {
		var c = ca[i];
		while (c.charAt(0)==' ') c = c.substring(1,c.length);
		if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
	}
	return null;
}
</script>
<?
	$_funcoesJSCookieCuspidas=true;
}
function mandaCsv(&$csv, $nome=null){
	set_time_limit(0);
	@ob_end_clean();//Zera o buffer para mandar o csv
	header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");
	header("Last-Modified: " . gmdate("D, d M Y H:i:s") . " GMT");
	//header('Expires: ' . gmdate('D, d M Y H:i:s') . ' GMT');
	header('Pragma: no-cache');
	header("Cache-Control: ");
	//header("Cache-control: private");
	header("Content-length: ".(string)(strlen($csv)));
	header("Content-type: application/csv");
	//header("Content-type: application/octet-stream");
	header("Content-disposition: attachment; filename=".($nome?$nome:'lista.csv'));
	//header("Connection: close");
	ignore_user_abort(true);//Deve estar ligado no php.ini mas é melhor garantir
	echo $csv;//Manda o bruto
	flush();
	exit;
}
function limpaTexto($txt){
	$translate_to_empty=',.-()!/\\´`~^¨';
	$translate_from='áàâãéêíóôõúçÁÀÂÃÉÊÍÓÔÕÚÇ';
	$translate_to  ='aaaaeeioooucAAAAEEIOOOUC';
	//echo nl2br(print_r(mb_str_split($translate_from), true))."<br/>";exit;
	if (false && mb_detect_encoding($txt,'UTF-8',true)){
		$ttosize=strlen($translate_to);
		$tfrom=array();
		$tto=array();
		for($i=0;$i<strlen($translate_from);$i++){
			$tfrom[]=utf8_encode($translate_from[$i]);
			if ($i<$ttosize) $tto[]=utf8_encode($translate_to[$i]);
		}
		return str_replace($tfrom, $tto, $txt);
	}
	return str_replace(mb_str_split($translate_from), mb_str_split($translate_to), $txt);
}
if (!function_exists('mb_str_split')){
	function mb_str_split($str, $len = 1) {
		$arr		= [];
		$length 	= mb_strlen($str, 'UTF-8');
		for ($i = 0; $i < $length; $i += $len) {
			$arr[] = mb_substr($str, $i, $len, 'UTF-8');
		}
		return $arr;
	}
}

function connDBSitePassaporte(){
	global $_configPASSDbPers, $_configPASSDbType, $_configPASSDbServer, $_configPASSDbUser, $_configPASSDbPass, $_configPASSDbName;
	$conn = null;
	ADOLoadCode($_configPASSDbType);
	$conn = ADONewConnection();
	if (! @$conn->Connect($_configPASSDbServer, $_configPASSDbUser, $_configPASSDbPass, $_configPASSDbName)) {
		echo "Erro conectando ao DB.";
	    exit;
	}
	return $conn;
}

function &connDBSiteFilhoPassaporte(&$dbpass, $sipa2_cod){
	$sql = "SELECT SIPA2_URL, SIPA2_TYPE_DB, SIPA2_NOM_DB, SIPA2_USU_DB, SIPA2_PW_DB
	FROM TB2_SIPA
	WHERE SIPA2_COD = ?
	";
	if($rec = $dbpass->executeBind($sql, array(array("integer", $sipa2_cod)))){
		$server=$dbpass->tiraVarchar($rec->fields[0]);
		$type=$dbpass->tiraVarchar($rec->fields[1]);
		$dbName=$dbpass->tiraVarchar($rec->fields[2]);
		$user=$dbpass->tiraVarchar($rec->fields[3]);
		$pwd=$dbpass->tiraVarchar($rec->fields[4]);
	}
	$conn = null;
	ADOLoadCode($type);
	$conn = ADONewConnection();
	if (! @$conn->Connect($erver, $user, $pwd, $dbName)) {
		echo "Erro conectando ao DB.";
	    exit;
	}
	return $conn;
}

function insereDbPassaporte(&$dbpass){
	global $_configUSA_PASSAPORTE, $_configDbType, $_configDbServer, $_configDbUser, $_configDbPass, $_configDbName, $_configDominio;
	if (@!$_configUSA_PASSAPORTE) return;
	// não existe o BD na base do passaporte, insere o BD na base depois o gajo no passaporte;
	$urlsipa = $_configDbServer;
	if(strtolower($_configDbServer)=='localhost') $urlsipa = $_configDominio;
	$seqSipa = $dbpass->getSeq("TB2_SIPA", "SIPA2_COD");
	$sql = "
	INSERT INTO TB2_SIPA
	(SIPA2_COD, SIPA2_URL, SIPA2_TYPE_DB, SIPA2_NOM_DB, SIPA2_USU_DB, SIPA2_PW_DB)
	VALUES (?, ?,?,?,?,?)
	";
	if($dbpass->ExecuteBind($sql, array(array("integer", $seqSipa), array("varchar",$urlsipa), array("varchar",$_configDbType), array("varchar",$_configDbName), array("varchar",$_configDbUser), array("varchar",$_configDbPass)))){
		return $seqSipa;
	} else {
		$dbpass->RollbackTrans();
		return false;
	}
}
function getSipaPassaporte(&$dbpass){
	global $_configUSA_PASSAPORTE, $_configDbName;
	if (@!$_configUSA_PASSAPORTE) return;
	$sql_sipa = "SELECT SIPA2_NOM_DB, SIPA2_COD FROM TB2_SIPA WHERE UPPER(SIPA2_NOM_DB) LIKE UPPER(?)";
	if ($rec = $dbpass->ExecuteBind($sql_sipa, array(array("varchar", $_configDbName)))){
		// abaixo: tentar pegar o sipa cod - ou se não pegar, insere o novo sipa na base do passaporte
		if(($nomeBase = $dbpass->tiraVarchar($rec->Fields(0))) && $nomeBase==$_configDbName){
			// existe o BD registrado - pega o codi sipa
			$sipa2_cod = $dbpass->tiraInteger($rec->Fields(1));
		} else {
			if (!$sipa2_cod = insereDbPassaporte($dbpass)) return false;
		}
		return $sipa2_cod;
	}else{
		$dbpass->_raiseError(__FILE__, __LINE__, $sql);
		return false;
	}
}
function checkExistLoginPassaporte(&$dbpass, $un){
	$sql="
	SELECT PASS2_USERNAME, PASS2_COD
	FROM TB2_PASS
	WHERE PASS2_USERNAME = ?
	";
	if($rec = $dbpass->executeBind($sql, array(array("varchar",$un)))){
		if ($rec->EOF) return false;
		if(($uname = $dbpass->tiraVarchar($rec->Fields(0))) && $uname==$un){
			$pass2_cod=$dbpass->tiraInteger($rec->Fields(1));
			global $_configDbName;
			$sql="SELECT PESS2_COD_SITE
			FROM TB2_PESI, TB2_SIPA
			WHERE TB2_PESI.SIPA2_COD=TB2_SIPA.SIPA2_COD
			AND TB2_SIPA.SIPA2_NOM_DB=?
			AND TB2_PESI.PASS2_COD=?
			";
			if($rec = $dbpass->executeBind($sql, array(array("varchar",$_configDbName), array("integer",$pass2_cod)))){
				if (!$rec->EOF){
					return $dbpass->tiraInteger($rec->Fields(0));
				}else{
					return true;
				}
			}else{
				$dbpass->_raiseError(__FILE__, __LINE__, $sql);
				return false;
			}
			return true;
		} else {
			return false;
		}
	}
}
function insereUsuarioPassaporte(&$dbpass, $uname, $pwd, $pess2_cod){
	global $_configUSA_PASSAPORTE;
	if (@!$_configUSA_PASSAPORTE) return;
	$debug=true;
	if ($sipa2_cod=getSipaPassaporte($dbpass)){
		if ($debug) echo("Pegou sipa $sipa2_cod<br>");
		$dbpass->BeginTrans();
		$seqPass = $dbpass->getSeq("TB2_PASS", "PASS2_COD");
		$sql = "
		INSERT INTO TB2_PASS
		(PASS2_COD, PASS2_USERNAME, PASS2_PASSWORD, SIPA2_COD, PASS2_DAT_HR_INI)
		VALUES (?, ?, ?, ?, ?)
		";
		if($dbpass->ExecuteBind($sql, array(array("integer", $seqPass), array('varchar', $uname), array('varchar', $pwd), array('integer', $sipa2_cod), array('Date', time())))){
			// inseriu na PASS - criou o código do passaporte
			if ($debug){
				echo $sipa2_cod."<---- SIPA2_COD<br>";
				echo $seqPass."<---- seqPass<br>";
				echo $pess2_cod."<---- pess2_cod<br>";
			}
			$sqlpesi = "
			INSERT INTO TB2_PESI
			(SIPA2_COD, PASS2_COD, PESS2_COD_SITE)
			VALUES
			(?,?,?)
			";
			if($dbpass->ExecuteBind($sqlpesi, array(array('integer', $sipa2_cod), array('integer', $seqPass), array('integer', $pess2_cod)))){
				// inseriu na PESI
				//$dbpass->RollbackTrans();
				if ($debug) echo("Inseriu gajo na PESI<br>");
				$dbpass->CommitTrans();
				return true;
			} else {
				// não inseriu na pesi
				$dbpass->_raiseError(__FILE__, __LINE__, $sqlpesi);
				echo "Não fiz o execbind de insert na PESI<br>";
				$dbpass->RollbackTrans();
				return false;
			}
		} else {
			if ($debug) echo("Não pegou sipa <br>");
			// não inseriu na pass
			$dbpass->_raiseError(__FILE__, __LINE__, $sql);
			echo "Não fiz o execbind de insert no PASS<br>";
			$dbpass->RollbackTrans();
			return false;
		}
	}else{
		echo "Não foi possível conectar-se ao site Passaporte.<br>";
		return false;
	}
}
function atribObjeto(&$obj, &$newObj, $isRef = false) {
	if(is_object($obj)) {
		foreach(get_object_vars($obj) as $key => $value) unset($obj->$key);

		if(!$isRef)
			foreach(get_object_vars($newObj) as $key => $value) $obj->$key = $value;
		else
			foreach(get_object_vars($newObj) as $key => $value) $obj->$key =& $newObj->$key;
	} else {
		$newObj = $obj;
	}
}
function &getUserGeradorHtml($roles){
	$user=&FACTORY::PESSOA();
	$user->pess2_cod=0;
	$user->pess2_nom='Usuário';
	$user->pess2_email='<EMAIL>';
	$user->pess2_username='usuario';
	$user->pess2_aceita_email=true;
	$user->logado=true;
	$user->roles=$roles;
	$user->mapaPermissoesDire=&$user->montaMapaPermissoesDire();
	$_emGeradorHtml=true;
	$user->geradorHtml=true;
	//echo (nl2br(print_r($user, true))."<br>");
	return $user;
}
function getTitlePag(){
	global $aqui, $_configNomeSite;
	if($aqui->path3_nom){
		$t = $aqui->path3_nom;
	} else {
		if($aqui->myDire->dire3_nom){
			$t = $aqui->myDire->dire3_nom;
		} else {
			$t = $_configNomeSite;
		}
	}
	return $t;
}
function getDescriptionPag(){
	global $aqui, $_config_description;
	if($aqui->path3_descr){
		$d = $aqui->path3_descr;
	} else {
		if(@$_config_description){
			$d = $_config_description;
		} else {
			$d = '';
		}
	}
	return $d;
}
function getKeywordsPag(){
	global $aqui, $_config_keywords;
	if($aqui->path3_key){
		$k = $aqui->path3_key;
	} else {
		if(@$_config_keywords){
			$k = $_config_keywords;
		} else {
			$k = '';
		}
	}
	return $k;
}
function number_format_plus($val){
	if ($val==intval($val))return number_format($val, 0, ',', '.');
	$casas=1;
	while($val*pow(10, $casas)!=intval($val*pow(10, $casas))){
		$casas++;
		if ($casas>12) break;
	}
	return number_format($val, $casas, ',', '.');
}
function isInteger($val){
	if (!is_scalar($val) || is_bool($val)) {
		return false;
	}
	if (@is_float($val + 0) && ($val + 0) > PHP_INT_MAX) {
		return false;
	}
	return is_float($val) ? false : preg_match('~^((?:\+|-)?[0-9]+)$~', $val);
}
function cleanPost(){
	cleanArrayXss($_POST);
}
function cleanGet(){
	cleanArrayXss($_GET);
}
function cleanRequest(){
	cleanArrayXss($_REQUEST);
}
function cleanArrayXss(&$a, $maxLength=null){
	array_walk($a, 'cleanVarXss', $maxLength);
}
function cleanWalkXss(&$item, $key){
	$item=cleanVarXss($item);
}
function cleanVarXss(&$val, $maxLength=null){
	if (is_null($val)) return null;
	if ($val=='') return '';
	if (is_array($val)) return cleanArrayXss($val, $maxLength);
	if ($maxLength && strlen($val)>$maxLength) return '';
	$val=str_replace(array("<",">"), array("&lt;","&gt;"), $val);
	$val=str_ireplace(array('onmouse','onchange','onclick'), '', $val);
	$val=preg_replace('/eval\s*\((.*)\)/i','', $val);
	$val=preg_replace('/["\'][\s]*((?i)javascript):(.*)["\']/i', '', $val);
	$val=preg_replace('/((?i)script)/i', '', $val);
	return $val;
}
function calculaDvCpf($cpf){
	$dv='';
	for ($t = 9; $t < 11; $t++) {
		for ($d = 0, $c = 0; $c < $t; $c++) {
			$d += $cpf[$c] * (($t + 1) - $c);
		}
		$dv.=((10 * $d) % 11) % 10;
	}
	return $dv;
}
function verificaCpf($cpf) {
	if(empty($cpf)) return false;
	$cpf = soNumeros($cpf);
	$cpf = str_pad($cpf, 11, '0', STR_PAD_LEFT);
	if (strlen($cpf) != 11) {
		return false;
	}else if ($cpf == '00000000000' || $cpf == '11111111111' || $cpf == '22222222222' || $cpf == '33333333333' || $cpf == '44444444444' || $cpf == '55555555555' || $cpf == '66666666666' || $cpf == '77777777777' || $cpf == '88888888888' || $cpf == '99999999999') {
		return false;
	 } else {
		$dv=calculaDvCpf($cpf);
		return $dv==$cpf[9].$cpf[10];
	}
}
function formataCpf($cpf){
	$cpf='00000000000'.soNumeros($cpf);
	$cpff=substr($cpf, -11, 3).'.'.substr($cpf, -8, 3).'.'.substr($cpf, -5, 3).'-'.substr($cpf, -2);
	return $cpff;
}
function calculaDvCnpj($cnpj){
	$soma1 = ($cnpj[0] * 5) +
	($cnpj[1] * 4) +
	($cnpj[2] * 3) +
	($cnpj[3] * 2) +
	($cnpj[4] * 9) +
	($cnpj[5] * 8) +
	($cnpj[6] * 7) +
	($cnpj[7] * 6) +
	($cnpj[8] * 5) +
	($cnpj[9] * 4) +
	($cnpj[10] * 3) +
	($cnpj[11] * 2);
	$resto = $soma1 % 11;
	$digito1 = $resto < 2 ? 0 : 11 - $resto;
	$soma2 = ($cnpj[0] * 6) +
	($cnpj[1] * 5) +
	($cnpj[2] * 4) +
	($cnpj[3] * 3) +
	($cnpj[4] * 2) +
	($cnpj[5] * 9) +
	($cnpj[6] * 8) +
	($cnpj[7] * 7) +
	($cnpj[8] * 6) +
	($cnpj[9] * 5) +
	($cnpj[10] * 4) +
	($cnpj[11] * 3) +
	($cnpj[12] * 2);
	$resto = $soma2 % 11;
	$digito2 = $resto < 2 ? 0 : 11 - $resto;
	return $digito1.$digito2;
}
function verificaCnpj($cnpj) {
	$cnpj=soNumeros($cnpj);
	if (strlen($cnpj) > 14) return false;
	$cnpj=substr('00000000000000'.$cnpj, -14);
	$dv=calculaDvCnpj($cnpj);
	if ($cnpj == "00000000000000" || 
		$cnpj == "11111111111111" || 
		$cnpj == "22222222222222" || 
		$cnpj == "33333333333333" || 
		$cnpj == "44444444444444" || 
		$cnpj == "55555555555555" || 
		$cnpj == "66666666666666" || 
		$cnpj == "77777777777777" || 
		$cnpj == "88888888888888" || 
	$cnpj == "99999999999999") return false;
	return (($cnpj[12].$cnpj[13] == $dv));
}
function formataCnpj($cnpj){
	$cnpj=substr('00000000000000'.$cnpj, -14);
	$dv=substr($cnpj, -2);
	$fil=substr($cnpj, 8, 4);
	$num=substr($cnpj, 0, 2);
	$num.='.'.substr($cnpj, 2, 3);
	$num.='.'.substr($cnpj, 5, 3);
	return $num.'/'.$fil.'-'.$dv;
}
function manda404(){
	header("HTTP/1.0 404 Not Found");
	global $_configPag404;
	if (@$_configPag404){
		header("Location:".$_configPag404);
	}else{
		?><div style="text-align: center;"><span style="font-size:72px;">404</span><br/>
		<b style="font-size:24px;">Página não encontrada</b><br/>
		<i>Page not found</i></div><?
		flush();
	}
	exit;
}
function checaCriaDirePath($txtPath, $txtDire='', $conteudo="<?php\r\n?>", $nomeDire=null, $nomePath=null){
	if (empty($txtDire)==false){
		while (substr($txtDire, -1)=='/'){
			$txtDire=substr($txtDire, 0, -1);
		}
		if (substr($txtDire, 0,1)!='/'){
			//$wd = preg_replace( '~(\w)$~' , '$1' . DIRECTORY_SEPARATOR , realpath( getcwd() ) );
			$pa=dirname($_SERVER['PHP_SELF']);
			$txtDire=$pa.'/'.$txtDire;
		}
		$base=$_SERVER['DOCUMENT_ROOT'];
		if (!file_exists($base.$txtDire)){
			$pt=substr($txtDire,1);
			$art=explode('/', $pt);
			$last=$art[sizeof($art)-1];
			$cam='';
			foreach($art as $n){
				$pt='/'.$n;
				$cam.='/'.$n;
				if (file_exists($base.$pt)==false){
					//não testei o truque abaixo
					$pp=@file_get_contents($pt."index.php?action=__unsetphyspath__&ver=".md5($p.$_SERVER['DOCUMENT_ROOT']),'r');
					if (empty($pp)==false) {
						$base=dirname($pp);
					}else{
						mkdir($base.$pt);
						$base.=$pt;
						$dire=&FACTORY::DIRETORIO();
						$dire->getDireByPath($cam);
						if ($n==$last){
							$dire->dire3_nom=$nomeDire;
							$dire->update();
						}else{
							//echo "parcial"."<br/>";
						}
					}
				}else{
					$base.=$pt;
				}
			}
			$d=$base;
		}else{
			$d=$base.$txtDire;
		}
		//neste ponto, o diretório está criado
	}else{
		$p=$_SERVER['SCRIPT_FILENAME'];
		$d=dirname ($p);
		//diretório atual
	}
	$f=$d.DIRECTORY_SEPARATOR.$txtPath;
	if (file_exists($f)==false){
		if ($h=fopen($f, 'wb+')){
			fwrite($h, $conteudo);
			fclose($h);
		}
	}
}
function direNaRole($dire, $role='Default'){
	if (is_numeric($dire)){
		$d=&FACTORY::DIRETORIO();
		$d->loadByPk($dire);
	}elseif(is_object($dire)){
		$d=$dire;
	}else{
		$d=&FACTORY::DIRETORIO();
		$d->getDireByPath($dire);
	}
	if (is_numeric($role)){
		$r=&FACTORY::ROLE();
		$r->loadByPk($role);
	}elseif(is_object($role)){
		$r=$role;
	}else{
		if (strtolower($role)=='default'){
			global $roleDefault;
			$r=$roleDefault;
		}elseif(strtolower($role)=='adm'){
			global $roleAdm;
			$r=$roleAdm;
		}else{
			$r=&FACTORY::ROLE();
			$r->getByCodTxt($role);
		}
	}
	$diro=&FACTORY::DIRETORIO_ACESSIVEL_PELA_ROLE();
	if (!$diro->loadByPk($d->dire3_cod, $r->role2_cod)){
		$diro->dire3_cod=$d->dire3_cod;
		$diro->role2_cod=$r->role2_cod;
		$diro->diro3_exib=true;
		//echo nl2br(print_r($diro,true))."<br/>";
		$diro->insert();
	}else{
		$diro->diro3_exib=true;
		//echo nl2br(print_r($diro,true))."<br/>";
		$diro->update();
	}
}
function getForbidden($qual='restrito', $lo=null){
	global $layout, $_configForbiddenLayout, $aqui, $layoutURL, $acao;
	if (!$lo) $lo=$layout;
	if (isset($_configForbiddenLayout) && is_array($_configForbiddenLayout) && isset($_configForbiddenLayout[$aqui->myDire->dire3_txt.$aqui->path3_txt])) $lo=$_configForbiddenLayout[$aqui->myDire->dire3_txt.$aqui->path3_txt];
	if (!$lo) $lo='null_layout';
	$sr=getSiteRoot();
	if ($qual=='restrito' && file_exists($sr.DIRECTORY_SEPARATOR."layout/$lo/forbidden_restrito.inc")==false)$qual='logar';
	if (in_array($qual,array('logar', 'forbidden', 'total'))==false) $qual='total';
	$layoutURL = "/layout/$lo";
	require("layout/$lo/forbidden_".$qual.".inc");
}
function limpaParaFileSystem($nome){ // para retornar um nome válido de diretório ou arquivo
	global $txtLog;
	$enc=mb_detect_encoding($nome, 'UTF-8', true);
	if (isset($txtLog)) $txtLog.='nome '.$nome.' tem encoding '.$enc." e vai converter...\r\n";
	if ($enc=='UTF-8') $nome=utf8_decode($nome);
	if (isset($txtLog)) $txtLog.='nome convertido para utf-8 '.$nome."\r\n";
	$limpa = array_merge(array_map('chr', range(0,31)),array(" ","<", ">", "[", "]", "=", ":", ";", ",", "'", '"', "/", "\\", "|", '&', '?', '*', "$", "#", "~", "`", "!", "%", "+"));
	$resp = str_replace($limpa, "_", $nome);
	while (strpos($resp, '__')!==false) $resp=str_replace('__','_',$resp);
	if (isset($txtLog)) $txtLog.='nome convertido para utf-8 e limpo: '.$resp."\r\n";
	$resp=limpaTexto($resp);
	if (isset($txtLog)) $txtLog.='nome convertido para utf-8, limpo e com limpaTexto: '.$resp."\r\n";
	return $resp;
}
function varsFromRequest(){
	foreach($_REQUEST as $n=>$v){
		if (isset($GLOBALS[$n])) continue;
		$GLOBALS[$n]=$v;
	}
}
function showInBox($s){
?><div style="border:solid 1px gray;background-color:white;margin:2px;padding:4px;font-size:12px;font-family:arial,helvetica,sans-serif;"><?
	if (is_object($s)){
		echo nl2br(print_r($s, true));
	}else{
		echo $s;
	}
?></div><?
}
function cospeResetSenha($pess){
?>
<form action="" method="post">
<input type="hidden" name="reset" value="<?=$pess->pess2_username?>"/>
<input type="hidden" name="secret" value="<?=$_REQUEST['secret']?>"/>
<input type="hidden" name="action" value="reset_go"/>
<div style="padding:40px;">
	<div style="margin:0 auto;width:250px;font-family:arial,sans-serif;font-size:14px;text-align:right;">
		<div style="padding:5px;text-align:right;">
			<b>Crie uma nova senha</b>
		</div>
		<div style="padding:5px;text-align:right;">
			Senha: <input type="password" style="width:150px;" name="pw1"/>
		</div>
		<div style="padding:5px;text-align:right;">
			Confirme: <input type="password" style="width:150px;" name="pw2"/>
		</div>
		<div style="padding:5px;text-align:right;">
			<button type="submit" style="width:150px;">Gravar senha</button>
		</div>
	</div>
</div>
</form>
<?
}
function doResetSenha($pess){
	$pw=trim($_REQUEST['pw1']);
	$pw2=trim($_REQUEST['pw2']);
	if ($pw!=$pw2) return 'Senhas não coincidem / Passwords don\'t match';
	if (strlen($pw)<3) return 'Senha muito curta / Password too short';
	$pess->pess2_crypt=password_hash($pw, PASSWORD_DEFAULT);
	$pess->pess2_password=null;
	if ($pess->update()) return true;
	return false;
}
function formataBytes($bytes){
	if ($bytes>=1024){
		$kbytes=$bytes/1024;
		if ($kbytes>=1024){
			$mbytes=$kbytes/1024;
			if ($mbytes>=1024){
				$show=number_format($mbytes/1024,2, ",", ".");
				$show.=" GB";
			}else{
				$show=number_format($kbytes/1024,2, ",", ".");
				$show.=" MB";
			}
		}else{
			$show=number_format($kbytes,2, ",", ".");
			$show.=" kB";
		}
	}else{
		$show=number_format($bytes, 0, ",", ".");
		$show.=" B";
	}
	return $show;
}
function setCookieSeguro($name, $value='', $expire=0, $path='/', $domain=null, $secure=null, $httponly=true){
	if (!$domain){
		global $_configSessionCookiesAllowSubdomains;
		if (@$_configSessionCookiesAllowSubdomains==true){
			$he = explode('.', strtolower($_SERVER["HTTP_HOST"]));
			$hc = count($he);
			if ($hc == 1){
				$domain = preg_replace('/\d+/', '', $he[0]);
			} else {
				$domain = '';
				for($i=1;$i<$hc;$i++){$domain.='.'.$he[$i];}
			}
		}else{
			$domain=strtolower($_SERVER["HTTP_HOST"]);
		}
	}
	$samesite='strict';
	if (!$secure) $secure=(empty($_SERVER['HTTPS']) || $_SERVER['HTTPS']=='off'?false:true);
	if (PHP_VERSION_ID < 70300) {
		return setcookie($name, $value, $expire, $path . '; samesite=' . $samesite, $domain, $secure, $httponly);
	}
	return setcookie($name, $value, [
		'expires' => $expire,
		'path' => $path,
		'domain' => $domain,
		'samesite' => $samesite,
		'secure' => $secure,
		'httponly' => $httponly,
	]);
}
?>
