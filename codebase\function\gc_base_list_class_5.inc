<?php
class gc_base_list {
	static $table=null;
	static $item=null;
	static $db,$pk;
	// public function __construct(){
	// 	parent::__construct();
	// 	self::$db=$db_UM;
	// }
	// static function DB() {
	// 	global $db_UM;
	// 	return $db_UM;
	// }
	function moveFirst(){
		@$this->_rec->MoveFirst();
	}
	function toArray($keyField=null){
		$a=array();
		$cont=0;
		while($item=$this->getNextItem()){
			$a[($keyField?$item->$keyField:(($key=$item->getArrayKey())?$key:$cont++))]=$item;
		}
		return $a;
	}
	static function find($id,$params=null){
		$sql="SELECT * from ".self::$table." where ".self::$pk."=?";
		if ($r = self::DB()->ExecuteBind($sql, array(array("Integer",$id)))) {
			$i=new self::$item();
			
		}
	}
	function varTraceHtml(){
		return nl2br($this->varTrace());
	}
	function varTrace(){
		$tr=get_class($this).":
";
		$vars=get_object_vars($this);
		$ignore=array('aster','astercompleto','_rec', 'pks', 'types', 'tab');
		foreach($vars as $key=>$value){
			if (in_array(strtolower($key),$ignore)) continue;
			if (substr($key,0,3)=='db_') continue;
			if (substr($key,0,5)=='ADODB') continue;
			if ($value===true){
				$txtValue="true";
			}else if ($value===false){
				$txtValue="false";
			}else if ($value==null){
				$txtValue="null";
			}else if (is_numeric($value)){
				$txtValue=$value;
			}else if (is_array($value)){
				$txtValue="ARRAY:";
				if (count($value)==0){
					$txtValue.="vazio";
				}else{
					$txtValue=print_r($value, true);
				}
			}else if (is_object($value)){
				if (method_exists($value, 'varTrace')) $txtValue=$value->varTrace(); else $txtValue=print_r($value, true);
			}else{
				$txtValue="\"".$value."\"";
			}
			$tr.="\r\n".$key."=".$txtValue;
		}
		return $tr;
	}
}
?>